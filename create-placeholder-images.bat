@echo off
echo Creating placeholder images for pentesting slides...

REM Create slides directory if it doesn't exist
if not exist "assets_I\images\slides" mkdir "assets_I\images\slides"

REM Create simple text files as placeholders (will be replaced with actual images)
echo Terminal Interface - Introduction to Penetration Testing > "assets_I\images\slides\slide1-intro.png"
echo Terminal Interface - Penetration Testing Methodology > "assets_I\images\slides\slide2-methodology.png"
echo Terminal Interface - Information Gathering > "assets_I\images\slides\slide3-recon.png"
echo Terminal Interface - Network Scanning > "assets_I\images\slides\slide4-scanning.png"
echo Terminal Interface - Vulnerability Assessment > "assets_I\images\slides\slide5-vuln-assessment.png"
echo Terminal Interface - Web Application Security > "assets_I\images\slides\slide6-web-security.png"
echo Terminal Interface - Burp Suite > "assets_I\images\slides\slide7-burp-suite.png"
echo Terminal Interface - Network Exploitation > "assets_I\images\slides\slide8-network-exploit.png"
echo Terminal Interface - Metasploit Framework > "assets_I\images\slides\slide9-metasploit.png"
echo Terminal Interface - Post-Exploitation > "assets_I\images\slides\slide10-post-exploit.png"
echo Terminal Interface - Wireless Security > "assets_I\images\slides\slide11-wireless.png"
echo Terminal Interface - Social Engineering > "assets_I\images\slides\slide12-social-eng.png"
echo Terminal Interface - Mobile Security > "assets_I\images\slides\slide13-mobile-security.png"
echo Terminal Interface - Database Security > "assets_I\images\slides\slide14-database-security.png"
echo Terminal Interface - Cloud Security > "assets_I\images\slides\slide15-cloud-security.png"
echo Terminal Interface - API Security > "assets_I\images\slides\slide16-api-security.png"
echo Terminal Interface - Cryptography Testing > "assets_I\images\slides\slide17-crypto-testing.png"
echo Terminal Interface - Active Directory > "assets_I\images\slides\slide18-ad-security.png"
echo Terminal Interface - IoT Security > "assets_I\images\slides\slide19-iot-security.png"
echo Terminal Interface - Container Security > "assets_I\images\slides\slide20-container-security.png"
echo Terminal Interface - SCADA Security > "assets_I\images\slides\slide21-scada-security.png"
echo Terminal Interface - Red Team Operations > "assets_I\images\slides\slide22-red-team.png"
echo Terminal Interface - Malware Analysis > "assets_I\images\slides\slide23-malware-analysis.png"
echo Terminal Interface - Digital Forensics > "assets_I\images\slides\slide24-forensics.png"
echo Terminal Interface - Threat Modeling > "assets_I\images\slides\slide25-threat-modeling.png"
echo Terminal Interface - Compliance Testing > "assets_I\images\slides\slide26-compliance.png"
echo Terminal Interface - Automated Testing > "assets_I\images\slides\slide27-automation.png"
echo Terminal Interface - Physical Security > "assets_I\images\slides\slide28-physical-security.png"
echo Terminal Interface - Report Writing > "assets_I\images\slides\slide29-reporting.png"
echo Terminal Interface - Legal Ethics > "assets_I\images\slides\slide30-legal-ethics.png"
echo Terminal Interface - Evasion Techniques > "assets_I\images\slides\slide31-evasion.png"
echo Terminal Interface - Bug Bounty > "assets_I\images\slides\slide32-bug-bounty.png"
echo Terminal Interface - Emerging Threats > "assets_I\images\slides\slide33-emerging-threats.png"
echo Terminal Interface - Career Development > "assets_I\images\slides\slide34-career.png"
echo Terminal Interface - Future of Pentesting > "assets_I\images\slides\slide35-future.png"

echo Placeholder images created successfully!
echo Note: These are text placeholders. Replace with actual terminal interface images for better visual appeal.
pause
