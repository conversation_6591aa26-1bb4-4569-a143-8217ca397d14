<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>XSS Protection Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #0a0e17;
            color: #e6e6e6;
        }
        .test-section {
            background-color: #1a1a2e;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            border-left: 4px solid #0084ff;
        }
        .test-input {
            width: 100%;
            padding: 10px;
            margin: 10px 0;
            background-color: #2d2d3a;
            color: #e6e6e6;
            border: 1px solid #424266;
            border-radius: 4px;
        }
        .test-result {
            background-color: #0c0c16;
            padding: 15px;
            margin: 10px 0;
            border-radius: 4px;
            font-family: monospace;
        }
        .safe {
            border-left: 4px solid #00ff8c;
        }
        .vulnerable {
            border-left: 4px solid #ff3860;
        }
        button {
            background-color: #0084ff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0066cc;
        }
    </style>
</head>
<body>
    <h1>XSS Protection Test for Pentesting.html</h1>
    
    <div class="test-section">
        <h2>Test 1: Basic Script Injection</h2>
        <p>This test attempts to inject a basic script tag:</p>
        <input type="text" class="test-input" id="test1" value="&lt;script&gt;alert('XSS')&lt;/script&gt;" placeholder="Enter test payload">
        <button onclick="runTest1()">Test Sanitization</button>
        <div class="test-result" id="result1"></div>
    </div>
    
    <div class="test-section">
        <h2>Test 2: Event Handler Injection</h2>
        <p>This test attempts to inject event handlers:</p>
        <input type="text" class="test-input" id="test2" value="&lt;img src=x onerror=alert('XSS')&gt;" placeholder="Enter test payload">
        <button onclick="runTest2()">Test Sanitization</button>
        <div class="test-result" id="result2"></div>
    </div>
    
    <div class="test-section">
        <h2>Test 3: JavaScript URL Injection</h2>
        <p>This test attempts to inject javascript: URLs:</p>
        <input type="text" class="test-input" id="test3" value="javascript:alert('XSS')" placeholder="Enter test payload">
        <button onclick="runTest3()">Test Sanitization</button>
        <div class="test-result" id="result3"></div>
    </div>
    
    <div class="test-section">
        <h2>Test 4: Data URL Injection</h2>
        <p>This test attempts to inject data: URLs:</p>
        <input type="text" class="test-input" id="test4" value="data:text/html,&lt;script&gt;alert('XSS')&lt;/script&gt;" placeholder="Enter test payload">
        <button onclick="runTest4()">Test Sanitization</button>
        <div class="test-result" id="result4"></div>
    </div>

    <script>
        // Import the sanitization function from pentesting-script.js
        function sanitizeInput(input) {
            if (typeof input !== 'string') {
                return '';
            }
            // Remove any HTML tags, script tags, and dangerous characters
            return input
                .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
                .replace(/<[^>]*>/g, '')
                .replace(/javascript:/gi, '')
                .replace(/on\w+\s*=/gi, '')
                .replace(/data:/gi, '')
                .substring(0, 100);
        }

        function escapeHtml(text) {
            if (typeof text !== 'string') {
                return '';
            }
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }

        function runTest1() {
            const input = document.getElementById('test1').value;
            const sanitized = sanitizeInput(input);
            const result = document.getElementById('result1');
            
            result.innerHTML = `
                <strong>Original:</strong> ${escapeHtml(input)}<br>
                <strong>Sanitized:</strong> ${escapeHtml(sanitized)}<br>
                <strong>Status:</strong> ${sanitized.includes('<script>') ? 'VULNERABLE' : 'SAFE'}
            `;
            result.className = `test-result ${sanitized.includes('<script>') ? 'vulnerable' : 'safe'}`;
        }

        function runTest2() {
            const input = document.getElementById('test2').value;
            const sanitized = sanitizeInput(input);
            const result = document.getElementById('result2');
            
            result.innerHTML = `
                <strong>Original:</strong> ${escapeHtml(input)}<br>
                <strong>Sanitized:</strong> ${escapeHtml(sanitized)}<br>
                <strong>Status:</strong> ${sanitized.includes('onerror') ? 'VULNERABLE' : 'SAFE'}
            `;
            result.className = `test-result ${sanitized.includes('onerror') ? 'vulnerable' : 'safe'}`;
        }

        function runTest3() {
            const input = document.getElementById('test3').value;
            const sanitized = sanitizeInput(input);
            const result = document.getElementById('result3');
            
            result.innerHTML = `
                <strong>Original:</strong> ${escapeHtml(input)}<br>
                <strong>Sanitized:</strong> ${escapeHtml(sanitized)}<br>
                <strong>Status:</strong> ${sanitized.toLowerCase().includes('javascript:') ? 'VULNERABLE' : 'SAFE'}
            `;
            result.className = `test-result ${sanitized.toLowerCase().includes('javascript:') ? 'vulnerable' : 'safe'}`;
        }

        function runTest4() {
            const input = document.getElementById('test4').value;
            const sanitized = sanitizeInput(input);
            const result = document.getElementById('result4');
            
            result.innerHTML = `
                <strong>Original:</strong> ${escapeHtml(input)}<br>
                <strong>Sanitized:</strong> ${escapeHtml(sanitized)}<br>
                <strong>Status:</strong> ${sanitized.toLowerCase().includes('data:') ? 'VULNERABLE' : 'SAFE'}
            `;
            result.className = `test-result ${sanitized.toLowerCase().includes('data:') ? 'vulnerable' : 'safe'}`;
        }

        // Run all tests on page load
        window.onload = function() {
            runTest1();
            runTest2();
            runTest3();
            runTest4();
        };
    </script>
</body>
</html>
