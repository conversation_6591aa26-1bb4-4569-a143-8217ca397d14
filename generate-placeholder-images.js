// Generate placeholder images for slides
const fs = require('fs');
const path = require('path');

// Create SVG placeholder images for each slide
const slides = [
    { id: 1, title: "Introduction to Penetration Testing", category: "basics" },
    { id: 2, title: "Penetration Testing Methodology", category: "basics" },
    { id: 3, title: "Information Gathering & Reconnaissance", category: "basics" },
    { id: 4, title: "Network Scanning Techniques", category: "intermediate" },
    { id: 5, title: "Vulnerability Assessment", category: "intermediate" },
    { id: 6, title: "Web Application Security Testing", category: "intermediate" },
    { id: 7, title: "Burp Suite for Web Testing", category: "intermediate" },
    { id: 8, title: "Network Exploitation Techniques", category: "advanced" },
    { id: 9, title: "Metasploit Framework", category: "advanced" },
    { id: 10, title: "Post-Exploitation Techniques", category: "advanced" }
];

function generateSVGImage(slide) {
    const colors = {
        basics: '#00ff8c',
        intermediate: '#0084ff',
        advanced: '#ff3860'
    };
    
    const color = colors[slide.category] || '#00ff8c';
    
    return `<svg width="600" height="400" xmlns="http://www.w3.org/2000/svg">
        <defs>
            <style>
                .terminal-bg { fill: #1a1a2e; }
                .terminal-header { fill: #2d2d3a; }
                .terminal-text { fill: #e6e6e6; font-family: 'Courier New', monospace; font-size: 14px; }
                .terminal-prompt { fill: ${color}; font-family: 'Courier New', monospace; font-size: 14px; }
                .terminal-title { fill: #a0a0a0; font-family: 'Courier New', monospace; font-size: 12px; }
                .category-badge { fill: ${color}; }
                .category-text { fill: #ffffff; font-family: Arial, sans-serif; font-size: 12px; font-weight: bold; }
            </style>
        </defs>
        
        <!-- Terminal Background -->
        <rect class="terminal-bg" width="600" height="400" rx="8"/>
        
        <!-- Terminal Header -->
        <rect class="terminal-header" width="600" height="40" rx="8 8 0 0"/>
        
        <!-- Terminal Buttons -->
        <circle cx="20" cy="20" r="6" fill="#ff5f56"/>
        <circle cx="40" cy="20" r="6" fill="#ffbd2e"/>
        <circle cx="60" cy="20" r="6" fill="#27c93f"/>
        
        <!-- Terminal Title -->
        <text class="terminal-title" x="80" y="25">cyber-wolf@pentest:~$</text>
        
        <!-- Category Badge -->
        <rect class="category-badge" x="20" y="60" width="80" height="20" rx="4"/>
        <text class="category-text" x="60" y="73" text-anchor="middle">${slide.category.toUpperCase()}</text>
        
        <!-- Slide Title -->
        <text class="terminal-text" x="20" y="110" font-size="18" font-weight="bold">${slide.title}</text>
        
        <!-- Terminal Prompt -->
        <text class="terminal-prompt" x="20" y="150">root@kali:~#</text>
        <text class="terminal-text" x="120" y="150">nmap -sS -sV target.com</text>
        
        <!-- Terminal Output -->
        <text class="terminal-text" x="40" y="180">Starting Nmap 7.94</text>
        <text class="terminal-text" x="40" y="200">PORT     STATE SERVICE VERSION</text>
        <text class="terminal-text" x="40" y="220">22/tcp   open  ssh     OpenSSH 8.2p1</text>
        <text class="terminal-text" x="40" y="240">80/tcp   open  http    Apache httpd 2.4.41</text>
        <text class="terminal-text" x="40" y="260">443/tcp  open  https   Apache httpd 2.4.41</text>
        
        <!-- Cursor -->
        <rect x="40" y="280" width="8" height="15" fill="${color}">
            <animate attributeName="opacity" values="1;0;1" dur="1s" repeatCount="indefinite"/>
        </rect>
        
        <!-- Slide Number -->
        <text class="terminal-title" x="550" y="380" text-anchor="end">Slide ${slide.id}</text>
    </svg>`;
}

// Create slides directory if it doesn't exist
const slidesDir = path.join(__dirname, 'assets_I', 'images', 'slides');
if (!fs.existsSync(slidesDir)) {
    fs.mkdirSync(slidesDir, { recursive: true });
}

// Generate placeholder images for first 10 slides
slides.forEach(slide => {
    const svgContent = generateSVGImage(slide);
    const filename = `slide${slide.id}-${slide.title.toLowerCase().replace(/[^a-z0-9]/g, '-').replace(/-+/g, '-').replace(/^-|-$/g, '')}.svg`;
    const filepath = path.join(slidesDir, filename);
    
    fs.writeFileSync(filepath, svgContent);
    console.log(`Generated: ${filename}`);
});

console.log('Placeholder images generated successfully!');
