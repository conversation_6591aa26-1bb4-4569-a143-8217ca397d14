[{"code": 100, "name": "Continue", "category": "Informational", "description": "The server has received the request headers and the client should proceed to send the request body.", "pentesting_context": "Rarely encountered in penetration testing. May indicate server expects additional data.", "common_scenarios": ["Large file uploads", "Expect: 100-continue header usage"], "security_implications": "Generally not a security concern, but may reveal server behavior patterns."}, {"code": 200, "name": "OK", "category": "Success", "description": "The request has succeeded. The meaning of success depends on the HTTP method.", "pentesting_context": "Most common successful response. Indicates normal application behavior.", "common_scenarios": ["Successful GET requests", "Valid form submissions", "API responses"], "security_implications": "Normal response, but content should be analyzed for sensitive information disclosure."}, {"code": 201, "name": "Created", "category": "Success", "description": "The request has been fulfilled and resulted in a new resource being created.", "pentesting_context": "Indicates successful resource creation. Useful for testing POST/PUT endpoints.", "common_scenarios": ["User registration", "File uploads", "API resource creation"], "security_implications": "Check if unauthorized users can create resources or if input validation is bypassed."}, {"code": 204, "name": "No Content", "category": "Success", "description": "The server successfully processed the request but is not returning any content.", "pentesting_context": "Common in DELETE operations or when no data needs to be returned.", "common_scenarios": ["DELETE requests", "PUT updates", "AJAX requests"], "security_implications": "May indicate successful operations that should be logged or monitored."}, {"code": 301, "name": "Moved Permanently", "category": "Redirection", "description": "The resource has been permanently moved to a new location.", "pentesting_context": "Useful for discovering hidden directories or endpoints through redirects.", "common_scenarios": ["URL restructuring", "HTTPS redirects", "Directory changes"], "security_implications": "May reveal internal structure or lead to open redirects if not properly validated."}, {"code": 302, "name": "Found", "category": "Redirection", "description": "The resource temporarily resides under a different URI.", "pentesting_context": "Common in authentication flows and can indicate session management issues.", "common_scenarios": ["Login redirects", "Temporary maintenance pages", "Load balancing"], "security_implications": "Potential for session fixation attacks or open redirect vulnerabilities."}, {"code": 304, "name": "Not Modified", "category": "Redirection", "description": "The resource has not been modified since the version specified by request headers.", "pentesting_context": "Indicates caching behavior, may reveal information about resource modification times.", "common_scenarios": ["Browser caching", "CDN responses", "API caching"], "security_implications": "May leak information about when resources were last modified."}, {"code": 400, "name": "Bad Request", "category": "<PERSON><PERSON>", "description": "The server cannot process the request due to client error.", "pentesting_context": "Indicates input validation. Useful for testing injection attacks and malformed requests.", "common_scenarios": ["Invalid JSON", "Missing parameters", "Malformed requests"], "security_implications": "May reveal input validation logic and potential injection points."}, {"code": 401, "name": "Unauthorized", "category": "<PERSON><PERSON>", "description": "Authentication is required and has failed or has not been provided.", "pentesting_context": "Critical for testing authentication mechanisms and access controls.", "common_scenarios": ["Missing credentials", "Invalid tokens", "Expired sessions"], "security_implications": "Indicates authentication requirements. Test for bypass techniques and brute force protection."}, {"code": 403, "name": "Forbidden", "category": "<PERSON><PERSON>", "description": "The server understood the request but refuses to authorize it.", "pentesting_context": "Indicates authorization issues. Different from 401 - user is authenticated but lacks permissions.", "common_scenarios": ["Insufficient privileges", "IP restrictions", "Resource access denied"], "security_implications": "Test for privilege escalation and authorization bypass vulnerabilities."}, {"code": 404, "name": "Not Found", "category": "<PERSON><PERSON>", "description": "The server cannot find the requested resource.", "pentesting_context": "Useful for directory enumeration and discovering hidden resources.", "common_scenarios": ["Missing pages", "Deleted resources", "Incorrect URLs"], "security_implications": "May indicate information disclosure through error messages or timing attacks."}, {"code": 405, "name": "Method Not Allowed", "category": "<PERSON><PERSON>", "description": "The request method is not supported for the requested resource.", "pentesting_context": "Reveals allowed HTTP methods. Useful for testing HTTP verb tampering.", "common_scenarios": ["POST to GET-only endpoints", "DELETE on read-only resources"], "security_implications": "May reveal additional attack vectors through alternative HTTP methods."}, {"code": 406, "name": "Not Acceptable", "category": "<PERSON><PERSON>", "description": "The server cannot produce a response matching the list of acceptable values.", "pentesting_context": "Related to content negotiation. May reveal supported content types.", "common_scenarios": ["Unsupported Accept headers", "Content type mismatches"], "security_implications": "May reveal information about server capabilities and supported formats."}, {"code": 408, "name": "Request Timeout", "category": "<PERSON><PERSON>", "description": "The server timed out waiting for the request.", "pentesting_context": "May indicate DoS vulnerabilities or slow processing endpoints.", "common_scenarios": ["Slow connections", "Large uploads", "Complex processing"], "security_implications": "Potential for denial of service attacks through resource exhaustion."}, {"code": 409, "name": "Conflict", "category": "<PERSON><PERSON>", "description": "The request conflicts with the current state of the server.", "pentesting_context": "Often seen in race conditions or concurrent access scenarios.", "common_scenarios": ["Duplicate resource creation", "Version conflicts", "State mismatches"], "security_implications": "May indicate race condition vulnerabilities or business logic flaws."}, {"code": 413, "name": "Payload Too Large", "category": "<PERSON><PERSON>", "description": "The request is larger than the server is willing or able to process.", "pentesting_context": "Useful for testing file upload limits and potential buffer overflow conditions.", "common_scenarios": ["Large file uploads", "Oversized POST data", "DoS attempts"], "security_implications": "May indicate DoS vulnerabilities or insufficient input validation."}, {"code": 415, "name": "Unsupported Media Type", "category": "<PERSON><PERSON>", "description": "The media format of the requested data is not supported by the server.", "pentesting_context": "Reveals supported content types and may indicate parsing vulnerabilities.", "common_scenarios": ["Wrong Content-Type header", "Unsupported file formats"], "security_implications": "May reveal attack vectors through alternative content types."}, {"code": 422, "name": "Unprocessable Entity", "category": "<PERSON><PERSON>", "description": "The request was well-formed but contains semantic errors.", "pentesting_context": "Common in API testing, indicates validation logic and business rules.", "common_scenarios": ["Invalid JSON structure", "Business rule violations", "Validation errors"], "security_implications": "May reveal business logic and validation rules that can be exploited."}, {"code": 429, "name": "Too Many Requests", "category": "<PERSON><PERSON>", "description": "The user has sent too many requests in a given amount of time.", "pentesting_context": "Indicates rate limiting implementation. Test for bypass techniques.", "common_scenarios": ["API rate limiting", "Brute force protection", "DoS prevention"], "security_implications": "Rate limiting bypass may allow brute force attacks or resource exhaustion."}, {"code": 500, "name": "Internal Server Error", "category": "Server Error", "description": "The server encountered an unexpected condition that prevented it from fulfilling the request.", "pentesting_context": "Critical error that may reveal system information or indicate injection vulnerabilities.", "common_scenarios": ["Application crashes", "Database errors", "Unhandled exceptions"], "security_implications": "May leak sensitive information in error messages or indicate exploitable conditions."}, {"code": 501, "name": "Not Implemented", "category": "Server Error", "description": "The server does not support the functionality required to fulfill the request.", "pentesting_context": "May reveal unimplemented features or development endpoints.", "common_scenarios": ["Unsupported HTTP methods", "Missing functionality", "Development stubs"], "security_implications": "May indicate incomplete security implementations or debug endpoints."}, {"code": 502, "name": "Bad Gateway", "category": "Server Error", "description": "The server received an invalid response from an upstream server.", "pentesting_context": "Indicates proxy/gateway configuration. May reveal internal architecture.", "common_scenarios": ["Proxy errors", "Load balancer issues", "Upstream server failures"], "security_implications": "May reveal internal network structure or allow proxy-based attacks."}, {"code": 503, "name": "Service Unavailable", "category": "Server Error", "description": "The server is currently unavailable (overloaded or down for maintenance).", "pentesting_context": "May indicate DoS vulnerabilities or maintenance windows.", "common_scenarios": ["Server overload", "Maintenance mode", "Resource exhaustion"], "security_implications": "Potential for denial of service attacks or timing-based reconnaissance."}, {"code": 504, "name": "Gateway Timeout", "category": "Server Error", "description": "The server did not receive a timely response from an upstream server.", "pentesting_context": "Indicates timeout configurations and may reveal internal service dependencies.", "common_scenarios": ["Slow upstream services", "Network issues", "Database timeouts"], "security_implications": "May allow timing attacks or reveal information about internal services."}, {"code": 511, "name": "Network Authentication Required", "category": "Server Error", "description": "The client needs to authenticate to gain network access.", "pentesting_context": "Indicates captive portal or network-level authentication requirements.", "common_scenarios": ["WiFi captive portals", "Corporate network access", "Proxy authentication"], "security_implications": "May indicate additional authentication layers that need to be bypassed."}]