/* Blog-specific styles */

/* ========================================
   CRITICAL RESPONSIVE FIXES
   ======================================== */

/* Prevent horizontal overflow on all elements */
* {
    max-width: 100%;
    word-wrap: break-word;
    overflow-wrap: break-word;
}

/* Ensure all containers are responsive */
.container,
.challenges-section,
.advanced-techniques,
.blog-hero,
.ctf-categories,
.methodology-section,
.tools-section,
.learning-resources {
    width: 100% !important;
    max-width: 100% !important;
    overflow-x: hidden !important;
}

/* Force responsive behavior on all grids */
.challenges-grid,
.challenge-stats,
.categories-grid,
.methodology-steps,
.tools-grid,
.resources-grid {
    width: 100% !important;
    max-width: 100% !important;
    box-sizing: border-box !important;
}

/* Ensure text doesn't break layout */
h1, h2, h3, h4, h5, h6, p, span, div {
    word-wrap: break-word !important;
    overflow-wrap: break-word !important;
    hyphens: auto;
}

/* Force responsive images */
img, video, iframe, canvas, svg {
    max-width: 100% !important;
    height: auto !important;
    box-sizing: border-box !important;
}

/* Responsive table handling */
table {
    width: 100% !important;
    table-layout: fixed !important;
    overflow-x: auto !important;
}

/* Critical mobile fixes */
@media (max-width: 768px) {
    /* Force single column on mobile */
    .challenges-grid {
        grid-template-columns: 1fr !important;
        gap: 15px !important;
    }

    /* Stack filters vertically */
    .challenge-filters,
    .difficulty-filters {
        flex-direction: column !important;
        align-items: center !important;
    }

    /* Full width buttons on mobile */
    .filter-btn,
    .diff-filter-btn {
        width: 100% !important;
        max-width: 280px !important;
    }

    /* Responsive modal */
    .modal-content {
        width: 100% !important;
        height: 100vh !important;
        margin: 0 !important;
        border-radius: 0 !important;
    }

    /* Responsive technique accordions */
    .technique-header {
        padding: 15px !important;
    }

    .technique-content.active {
        padding: 20px 15px !important;
    }
}

/* ========================================
   CRITICAL RESPONSIVE FOUNDATION
   ======================================== */

/* Universal Box Model */
*, *::before, *::after {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

/* Root HTML Setup */
html {
    font-size: 16px;
    scroll-behavior: smooth;
    -webkit-text-size-adjust: 100%;
    -ms-text-size-adjust: 100%;
    -moz-text-size-adjust: 100%;
    height: 100%;
}

/* Body Foundation */
body {
    min-height: 100vh;
    overflow-x: hidden;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    text-rendering: optimizeLegibility;
    font-family: 'Poppins', sans-serif;
    line-height: 1.6;
    color: #e0e0e0;
    background: #1a1a1a;
}

/* Critical Container System */
.container {
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 15px;
}

/* Responsive Images & Media */
img, video, iframe {
    max-width: 100%;
    height: auto;
    display: block;
}

/* Responsive Typography Foundation */
h1, h2, h3, h4, h5, h6 {
    line-height: 1.2;
    margin-bottom: 1rem;
    font-weight: 600;
}

p {
    line-height: 1.6;
    margin-bottom: 1rem;
}

/* Universal Touch-Friendly Elements */
button, .btn, .filter-btn, .diff-filter-btn,
.technique-header, .challenge-card, .bookmark-btn,
.complete-btn-modal, .bookmark-btn-modal {
    min-height: 44px;
    min-width: 44px;
    touch-action: manipulation;
    -webkit-tap-highlight-color: transparent;
    cursor: pointer;
    transition: all 0.3s ease;
}

/* Prevent Horizontal Scroll */
.blog-hero, .challenges-section, .advanced-techniques,
.ctf-categories, .methodology-section, .tools-section,
.learning-resources, .footer {
    overflow-x: hidden;
    width: 100%;
}

/* Flexible Grid System */
.responsive-grid {
    display: grid;
    gap: 20px;
    width: 100%;
}

/* Mobile-First Responsive Breakpoints */
.responsive-grid {
    grid-template-columns: 1fr;
}

@media (min-width: 576px) {
    .container { padding: 0 20px; }
    .responsive-grid { grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); }
}

@media (min-width: 768px) {
    .container { padding: 0 25px; }
    .responsive-grid { grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); }
}

@media (min-width: 992px) {
    .container { padding: 0 30px; }
    .responsive-grid { grid-template-columns: repeat(auto-fit, minmax(350px, 1fr)); }
}

@media (min-width: 1200px) {
    .container { max-width: 1200px; padding: 0 40px; }
    .responsive-grid { grid-template-columns: repeat(auto-fit, minmax(380px, 1fr)); }
}

/* Blog Hero Section */
.blog-hero {
    background: linear-gradient(135deg, var(--raisin-black-1) 0%, var(--raisin-black-2) 100%);
    padding: 150px 0 100px;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.blog-hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="%23333" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
    opacity: 0.1;
}

.blog-hero-content {
    position: relative;
    z-index: 2;
}

.blog-hero-title {
    font-family: var(--ff-oswald);
    font-size: clamp(2.5rem, 5vw, 4rem);
    color: var(--white);
    margin-bottom: 20px;
    text-transform: uppercase;
    letter-spacing: 2px;
}

.blog-hero-subtitle {
    font-size: clamp(1rem, 2vw, 1.2rem);
    color: var(--light-gray);
    margin-bottom: 40px;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
    line-height: 1.6;
}

.blog-hero-stats {
    display: flex;
    justify-content: center;
    gap: 40px;
    flex-wrap: wrap;
}

.stat-item {
    text-align: center;
}

.stat-number {
    display: block;
    font-family: var(--ff-oswald);
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--orange);
    margin-bottom: 5px;
}

.stat-label {
    font-size: 0.9rem;
    color: var(--light-gray);
    text-transform: uppercase;
    letter-spacing: 1px;
}

/* CTF Categories */
.ctf-categories {
    padding: 100px 0;
    background: var(--raisin-black-2);
}

.categories-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
    margin-top: 50px;
}

.category-card {
    background: var(--raisin-black-1);
    padding: 40px 30px;
    border-radius: 12px;
    text-align: center;
    transition: all 0.3s ease;
    border: 2px solid transparent;
    cursor: pointer;
    position: relative;
    overflow: hidden;
}

.category-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 165, 0, 0.1), transparent);
    transition: left 0.5s ease;
}

.category-card:hover::before {
    left: 100%;
}

.category-card:hover {
    border-color: var(--orange);
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(255, 165, 0, 0.2);
}

.category-icon {
    width: 80px;
    height: 80px;
    background: var(--orange);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 20px;
    transition: all 0.3s ease;
}

.category-icon ion-icon {
    font-size: 2.5rem;
    color: var(--white);
}

.category-card:hover .category-icon {
    transform: scale(1.1);
    box-shadow: 0 0 20px rgba(255, 165, 0, 0.5);
}

.category-card h3 {
    font-family: var(--ff-oswald);
    font-size: 1.5rem;
    color: var(--white);
    margin-bottom: 15px;
    text-transform: uppercase;
}

.category-card p {
    color: var(--light-gray);
    line-height: 1.6;
    margin-bottom: 20px;
}

.challenge-count {
    display: inline-block;
    background: var(--orange);
    color: var(--white);
    padding: 5px 15px;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 600;
}

/* Methodology Section */
.methodology-section {
    padding: 100px 0;
    background: var(--raisin-black-1);
}

.methodology-steps {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 30px;
    margin-top: 50px;
}

.step-card {
    background: var(--raisin-black-2);
    padding: 40px 30px;
    border-radius: 12px;
    position: relative;
    border-left: 4px solid var(--orange);
    transition: all 0.3s ease;
}

.step-card:hover {
    transform: translateX(10px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.step-number {
    position: absolute;
    top: -15px;
    left: 30px;
    background: var(--orange);
    color: var(--white);
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-family: var(--ff-oswald);
    font-weight: 700;
    font-size: 1.2rem;
}

.step-card h3 {
    font-family: var(--ff-oswald);
    font-size: 1.4rem;
    color: var(--white);
    margin-bottom: 15px;
    margin-top: 10px;
    text-transform: uppercase;
}

.step-card p {
    color: var(--light-gray);
    line-height: 1.6;
    margin-bottom: 20px;
}

.step-tools {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
}

.tool-tag {
    background: var(--raisin-black-3);
    color: var(--orange);
    padding: 5px 12px;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 500;
    border: 1px solid var(--orange);
}

/* Tools Section */
.tools-section {
    padding: 100px 0;
    background: var(--raisin-black-2);
}

.tools-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 40px;
    margin-top: 50px;
}

.tool-category {
    background: var(--raisin-black-1);
    padding: 40px 30px;
    border-radius: 12px;
    border-top: 4px solid var(--orange);
}

.tool-category h3 {
    font-family: var(--ff-oswald);
    font-size: 1.4rem;
    color: var(--white);
    margin-bottom: 25px;
    text-transform: uppercase;
    text-align: center;
}

.tool-list {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.tool-item {
    background: var(--raisin-black-2);
    padding: 20px;
    border-radius: 8px;
    border-left: 3px solid var(--orange);
    transition: all 0.3s ease;
}

.tool-item:hover {
    background: var(--raisin-black-3);
    transform: translateX(5px);
}

.tool-name {
    display: block;
    font-weight: 600;
    color: var(--orange);
    margin-bottom: 5px;
    font-size: 1.1rem;
}

.tool-desc {
    color: var(--light-gray);
    font-size: 0.9rem;
    line-height: 1.4;
}

/* Challenges Section */
.challenges-section {
    padding: 100px 0;
    background: var(--raisin-black-1);
}

/* ========================================
   RESPONSIVE CHALLENGE FILTERS
   ======================================== */

.challenge-filters {
    display: flex;
    justify-content: center;
    flex-wrap: wrap;
    gap: 10px;
    margin: 30px 0;
    padding: 0 10px;
}

.filter-btn {
    background: #2a2a2a;
    color: #e0e0e0;
    border: 2px solid #3a3a3a;
    padding: 12px 20px;
    border-radius: 25px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 1px;
    font-size: 0.9rem;
    white-space: nowrap;
    min-height: 44px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.filter-btn:hover,
.filter-btn.active {
    background: #ff6b35;
    color: #ffffff;
    border-color: #ff6b35;
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(255, 107, 53, 0.3);
}

/* Mobile-First Filter Responsive Design */
@media (max-width: 575px) {
    .challenge-filters {
        flex-direction: column;
        align-items: center;
        gap: 8px;
        margin: 20px 0;
    }

    .filter-btn {
        width: 100%;
        max-width: 280px;
        padding: 14px 20px;
        font-size: 0.85rem;
    }
}

@media (min-width: 576px) and (max-width: 767px) {
    .challenge-filters {
        gap: 12px;
        margin: 25px 0;
    }

    .filter-btn {
        padding: 12px 18px;
        font-size: 0.9rem;
    }
}

@media (min-width: 768px) {
    .challenge-filters {
        gap: 15px;
        margin: 35px 0;
    }

    .filter-btn {
        padding: 12px 25px;
        font-size: 0.9rem;
    }
}

/* ========================================
   RESPONSIVE SEARCH CONTAINER
   ======================================== */

.search-container {
    position: relative;
    max-width: 500px;
    margin: 0 auto 30px;
    padding: 0 15px;
    width: 100%;
}

.search-input {
    width: 100%;
    padding: 15px 50px 15px 20px;
    background: #2a2a2a;
    border: 2px solid #3a3a3a;
    border-radius: 25px;
    color: #ffffff;
    font-size: 1rem;
    transition: all 0.3s ease;
    min-height: 50px;
    box-sizing: border-box;
}

.search-input:focus {
    outline: none;
    border-color: #ff6b35;
    box-shadow: 0 0 15px rgba(255, 107, 53, 0.3);
    background: #333333;
}

.search-input::placeholder {
    color: #b0b0b0;
    opacity: 1;
}

.search-icon {
    position: absolute;
    right: 35px;
    top: 50%;
    transform: translateY(-50%);
    color: #ff6b35;
    font-size: 1.2rem;
    pointer-events: none;
}

/* Mobile-First Search Design */
@media (max-width: 575px) {
    .search-container {
        margin: 0 auto 20px;
        padding: 0 10px;
    }

    .search-input {
        padding: 12px 45px 12px 15px;
        font-size: 0.9rem;
        min-height: 48px;
        border-radius: 20px;
    }

    .search-icon {
        right: 25px;
        font-size: 1.1rem;
    }
}

@media (min-width: 576px) and (max-width: 767px) {
    .search-container {
        margin: 0 auto 25px;
        max-width: 400px;
    }

    .search-input {
        padding: 14px 48px 14px 18px;
        font-size: 0.95rem;
    }

    .search-icon {
        right: 30px;
    }
}

@media (min-width: 768px) {
    .search-container {
        margin: 0 auto 35px;
        max-width: 500px;
    }

    .search-input {
        padding: 15px 50px 15px 20px;
        font-size: 1rem;
    }

    .search-icon {
        right: 35px;
    }
}

/* ========================================
   ENHANCED RESPONSIVE CHALLENGE STATISTICS
   ======================================== */

.challenge-stats-container {
    margin: 50px 0;
    padding: 40px 20px;
    background: linear-gradient(135deg, #1a1a1a 0%, #2a2a2a 50%, #1a1a1a 100%);
    border-radius: 20px;
    border: 2px solid #3a3a3a;
    position: relative;
    overflow: hidden;
}

.challenge-stats-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, transparent, #ff6b35, transparent);
}

.stats-header {
    text-align: center;
    margin-bottom: 40px;
}

.stats-header h3 {
    font-family: 'Oswald', sans-serif;
    font-size: 2rem;
    color: #ffffff;
    margin-bottom: 10px;
    text-transform: uppercase;
    letter-spacing: 2px;
}

.stats-header p {
    color: #b0b0b0;
    font-size: 1rem;
    max-width: 500px;
    margin: 0 auto;
    line-height: 1.6;
}

.challenge-stats {
    display: grid;
    grid-template-columns: 1fr;
    gap: 20px;
    margin-bottom: 30px;
}

.stat-card {
    background: #2a2a2a;
    padding: 25px 20px;
    border-radius: 15px;
    border: 2px solid #3a3a3a;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 20px;
    position: relative;
    overflow: hidden;
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: linear-gradient(180deg, #ff6b35, #ff8c42);
    transform: scaleY(0);
    transition: transform 0.3s ease;
}

.stat-card:hover::before {
    transform: scaleY(1);
}

.stat-card:hover {
    border-color: #ff6b35;
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(255, 107, 53, 0.2);
}

.stat-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, #ff6b35, #ff8c42);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.stat-icon ion-icon {
    font-size: 1.8rem;
    color: #ffffff;
}

.stat-content {
    flex: 1;
    text-align: left;
}

.stat-value {
    display: inline-block;
    font-family: 'Oswald', sans-serif;
    font-size: 2.5rem;
    font-weight: 700;
    color: #ff6b35;
    line-height: 1;
    margin-right: 5px;
}

.stat-plus {
    font-family: 'Oswald', sans-serif;
    font-size: 1.8rem;
    font-weight: 700;
    color: #ff6b35;
    vertical-align: top;
}

.stat-label {
    display: block;
    color: #ffffff;
    font-size: 1rem;
    font-weight: 600;
    margin: 5px 0 3px;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.stat-description {
    display: block;
    color: #b0b0b0;
    font-size: 0.85rem;
    line-height: 1.4;
}

.stats-footer {
    text-align: center;
    padding-top: 20px;
    border-top: 1px solid #3a3a3a;
}

.stats-highlight {
    display: inline-flex;
    align-items: center;
    gap: 10px;
    color: #ff6b35;
    font-size: 0.9rem;
    font-weight: 500;
    padding: 10px 20px;
    background: rgba(255, 107, 53, 0.1);
    border-radius: 25px;
    border: 1px solid rgba(255, 107, 53, 0.3);
}

.stats-highlight ion-icon {
    font-size: 1.1rem;
}

/* Mobile Responsive (320px - 575px) */
@media (max-width: 575px) {
    .challenge-stats-container {
        margin: 30px 0;
        padding: 25px 15px;
        border-radius: 15px;
    }

    .stats-header h3 {
        font-size: 1.5rem;
        margin-bottom: 8px;
    }

    .stats-header p {
        font-size: 0.9rem;
        padding: 0 10px;
    }

    .stats-header {
        margin-bottom: 25px;
    }

    .challenge-stats {
        gap: 15px;
        margin-bottom: 20px;
    }

    .stat-card {
        padding: 20px 15px;
        flex-direction: column;
        text-align: center;
        gap: 15px;
    }

    .stat-icon {
        width: 50px;
        height: 50px;
    }

    .stat-icon ion-icon {
        font-size: 1.5rem;
    }

    .stat-content {
        text-align: center;
    }

    .stat-value {
        font-size: 2rem;
    }

    .stat-plus {
        font-size: 1.5rem;
    }

    .stat-label {
        font-size: 0.9rem;
        margin: 3px 0 2px;
    }

    .stat-description {
        font-size: 0.8rem;
    }

    .stats-highlight {
        font-size: 0.8rem;
        padding: 8px 15px;
        flex-direction: column;
        gap: 5px;
        text-align: center;
    }
}

/* Tablet Responsive (576px - 767px) */
@media (min-width: 576px) and (max-width: 767px) {
    .challenge-stats-container {
        margin: 40px 0;
        padding: 30px 20px;
    }

    .stats-header h3 {
        font-size: 1.8rem;
    }

    .challenge-stats {
        grid-template-columns: repeat(2, 1fr);
        gap: 18px;
    }

    .stat-card {
        padding: 22px 18px;
    }

    .stat-value {
        font-size: 2.2rem;
    }

    .stat-plus {
        font-size: 1.6rem;
    }

    .stat-label {
        font-size: 0.95rem;
    }
}

/* Desktop Responsive (768px+) */
@media (min-width: 768px) {
    .challenge-stats-container {
        margin: 50px 0;
        padding: 40px 30px;
    }

    .stats-header h3 {
        font-size: 2rem;
    }

    .challenge-stats {
        grid-template-columns: repeat(4, 1fr);
        gap: 20px;
    }

    .stat-card {
        padding: 25px 20px;
    }

    .stat-value {
        font-size: 2.5rem;
    }

    .stat-plus {
        font-size: 1.8rem;
    }
}

/* Large Desktop (1200px+) */
@media (min-width: 1200px) {
    .challenge-stats-container {
        padding: 50px 40px;
    }

    .challenge-stats {
        gap: 25px;
    }

    .stat-card {
        padding: 30px 25px;
    }

    .stat-icon {
        width: 70px;
        height: 70px;
    }

    .stat-icon ion-icon {
        font-size: 2rem;
    }

    .stat-value {
        font-size: 2.8rem;
    }

    .stat-plus {
        font-size: 2rem;
    }

    .stat-label {
        font-size: 1.1rem;
    }

    .stat-description {
        font-size: 0.9rem;
    }
}

/* ========================================
   RESPONSIVE DIFFICULTY FILTERS
   ======================================== */

.difficulty-filters {
    display: flex;
    justify-content: center;
    flex-wrap: wrap;
    gap: 8px;
    margin: 15px 0;
    padding: 0 10px;
}

.diff-filter-btn {
    background: #2a2a2a;
    color: #e0e0e0;
    border: 2px solid #3a3a3a;
    padding: 10px 18px;
    border-radius: 20px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 500;
    font-size: 0.85rem;
    min-height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    white-space: nowrap;
}

.diff-filter-btn:hover,
.diff-filter-btn.active {
    background: #ff6b35;
    color: #ffffff;
    border-color: #ff6b35;
    transform: translateY(-1px);
    box-shadow: 0 3px 10px rgba(255, 107, 53, 0.3);
}

/* Mobile-First Difficulty Filter Design */
@media (max-width: 575px) {
    .difficulty-filters {
        flex-direction: column;
        align-items: center;
        gap: 6px;
        margin: 15px 0;
    }

    .diff-filter-btn {
        width: 100%;
        max-width: 200px;
        padding: 12px 16px;
        font-size: 0.8rem;
    }
}

@media (min-width: 576px) and (max-width: 767px) {
    .difficulty-filters {
        gap: 10px;
        margin: 18px 0;
    }

    .diff-filter-btn {
        padding: 10px 16px;
        font-size: 0.85rem;
    }
}

@media (min-width: 768px) {
    .difficulty-filters {
        gap: 12px;
        margin: 20px 0;
    }

    .diff-filter-btn {
        padding: 10px 20px;
        font-size: 0.9rem;
    }
}

/* Search Suggestions */
.search-suggestions {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: var(--raisin-black-1);
    border: 2px solid var(--orange);
    border-top: none;
    border-radius: 0 0 12px 12px;
    max-height: 200px;
    overflow-y: auto;
    z-index: 10;
    display: none;
}

.suggestion-item {
    padding: 12px 20px;
    cursor: pointer;
    transition: background 0.2s ease;
    color: var(--light-gray);
}

.suggestion-item:hover {
    background: var(--raisin-black-2);
    color: var(--orange);
}

/* ========================================
   RESPONSIVE CHALLENGES GRID SYSTEM
   ======================================== */

.challenges-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 20px;
    margin-top: 40px;
    min-height: 400px;
    width: 100%;
    padding: 0;
}

/* Progressive Enhancement for Larger Screens */
@media (min-width: 576px) {
    .challenges-grid {
        grid-template-columns: 1fr;
        gap: 25px;
    }
}

@media (min-width: 768px) {
    .challenges-grid {
        grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
        gap: 25px;
    }
}

@media (min-width: 992px) {
    .challenges-grid {
        grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
        gap: 30px;
    }
}

@media (min-width: 1200px) {
    .challenges-grid {
        grid-template-columns: repeat(auto-fit, minmax(380px, 1fr));
        gap: 35px;
    }
}

@media (min-width: 1400px) {
    .challenges-grid {
        grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
        gap: 40px;
    }
}

.challenge-card {
    background: var(--raisin-black-2);
    border-radius: 12px;
    overflow: hidden;
    transition: all 0.3s ease;
    border: 2px solid transparent;
    cursor: pointer;
    position: relative;
}

.challenge-card:hover {
    border-color: var(--orange);
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(255, 165, 0, 0.2);
}

.challenge-card.completed {
    border-color: #4CAF50;
}

.challenge-card.completed::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(76, 175, 80, 0.1) 0%, transparent 50%);
    pointer-events: none;
}

.challenge-title-row {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 15px;
}

.challenge-actions {
    display: flex;
    gap: 8px;
    align-items: center;
}

.bookmark-btn {
    background: none;
    border: none;
    color: var(--light-gray);
    cursor: pointer;
    padding: 5px;
    border-radius: 50%;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.bookmark-btn:hover {
    color: var(--orange);
    background: rgba(255, 165, 0, 0.1);
}

.bookmark-btn.bookmarked {
    color: var(--orange);
}

.completed-badge {
    color: #4CAF50;
    font-size: 1.2rem;
    display: flex;
    align-items: center;
}

.challenge-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 6px;
    margin-top: 15px;
}

.tool-tag-small {
    background: var(--raisin-black-3);
    color: var(--orange);
    padding: 3px 8px;
    border-radius: 10px;
    font-size: 0.7rem;
    font-weight: 500;
    border: 1px solid rgba(255, 165, 0, 0.3);
}

.challenge-stats {
    display: flex;
    gap: 15px;
    align-items: center;
}

.progress-bar-small {
    width: 60px;
    height: 4px;
    background: var(--raisin-black-3);
    border-radius: 2px;
    overflow: hidden;
}

.progress-fill-small {
    height: 100%;
    background: linear-gradient(90deg, var(--orange), #e67e22);
    border-radius: 2px;
    transition: width 0.3s ease;
}

.challenge-header {
    padding: 25px;
    border-bottom: 1px solid var(--raisin-black-3);
}

.challenge-title {
    font-family: var(--ff-oswald);
    font-size: 1.3rem;
    color: var(--white);
    margin-bottom: 10px;
    text-transform: uppercase;
}

.challenge-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.challenge-category {
    background: var(--orange);
    color: var(--white);
    padding: 4px 12px;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
}

.challenge-difficulty {
    padding: 4px 12px;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
}

.difficulty-easy { background: #4CAF50; color: white; }
.difficulty-medium { background: #FF9800; color: white; }
.difficulty-hard { background: #F44336; color: white; }

.challenge-description {
    color: var(--light-gray);
    line-height: 1.5;
    font-size: 0.9rem;
}

.challenge-footer {
    padding: 20px 25px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.challenge-points {
    font-weight: 600;
    color: var(--orange);
    font-size: 1.1rem;
}

.challenge-solves {
    color: var(--light-gray);
    font-size: 0.9rem;
}

/* Modal Styles */
/* ========================================
   RESPONSIVE MODAL SYSTEM
   ======================================== */

.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.85);
    backdrop-filter: blur(5px);
    -webkit-backdrop-filter: blur(5px);
}

.modal-content {
    background: #1a1a1a;
    margin: 2% auto;
    padding: 0;
    border-radius: 12px;
    width: 95%;
    max-width: 900px;
    max-height: 95vh;
    overflow-y: auto;
    position: relative;
    border: 2px solid #ff6b35;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5);
}

.close-modal {
    position: absolute;
    top: 15px;
    right: 25px;
    color: #e0e0e0;
    font-size: 2rem;
    font-weight: bold;
    cursor: pointer;
    z-index: 1001;
    transition: all 0.3s ease;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
}

.close-modal:hover {
    color: #ff6b35;
    background: #3a3a3a;
    transform: rotate(90deg);
}

.modal-body {
    padding: 0;
    color: #e0e0e0;
    line-height: 1.6;
}

/* Mobile-First Modal Responsive Design */
@media (max-width: 575px) {
    .modal-content {
        width: 100%;
        height: 100vh;
        margin: 0;
        border-radius: 0;
        max-height: 100vh;
        border: none;
    }

    .close-modal {
        top: 10px;
        right: 15px;
        font-size: 1.5rem;
        width: 36px;
        height: 36px;
    }
}

@media (min-width: 576px) and (max-width: 767px) {
    .modal-content {
        width: 98%;
        margin: 2% auto;
        border-radius: 8px;
        max-height: 95vh;
    }

    .close-modal {
        top: 12px;
        right: 20px;
        font-size: 1.8rem;
    }
}

@media (min-width: 768px) and (max-width: 991px) {
    .modal-content {
        width: 90%;
        margin: 3% auto;
        max-width: 700px;
    }
}

@media (min-width: 992px) {
    .modal-content {
        width: 85%;
        margin: 3% auto;
        max-width: 900px;
    }
}

/* Enhanced Modal Content */
.modal-challenge-header {
    padding: 30px;
    border-bottom: 1px solid var(--raisin-black-3);
    background: linear-gradient(135deg, var(--raisin-black-2) 0%, var(--raisin-black-1) 100%);
}

.challenge-title-section {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 20px;
    flex-wrap: wrap;
    gap: 15px;
}

.challenge-actions-modal {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.bookmark-btn-modal,
.complete-btn-modal {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 10px 16px;
    border: 2px solid var(--raisin-black-3);
    background: var(--raisin-black-2);
    color: var(--light-gray);
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.9rem;
    font-weight: 500;
}

.bookmark-btn-modal:hover,
.complete-btn-modal:hover {
    border-color: var(--orange);
    color: var(--orange);
    transform: translateY(-2px);
}

.bookmark-btn-modal.bookmarked {
    border-color: #FFD700;
    color: #FFD700;
}

.complete-btn-modal.completed {
    border-color: #4CAF50;
    color: #4CAF50;
}

.challenge-meta-modal {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 15px;
}

.challenge-progress-bar {
    flex: 1;
    min-width: 200px;
    height: 8px;
    background: var(--raisin-black-3);
    border-radius: 4px;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, var(--orange) 0%, #ff6b35 100%);
    transition: width 0.3s ease;
}

.modal-content-section {
    padding: 25px 30px;
    border-bottom: 1px solid var(--raisin-black-3);
}

.modal-content-section:last-child {
    border-bottom: none;
}

.section-header {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 20px;
}

.section-header ion-icon {
    font-size: 1.5rem;
    color: var(--orange);
}

.section-header h3 {
    margin: 0;
    color: var(--white);
    font-family: var(--ff-oswald);
    font-size: 1.3rem;
}

.description-content {
    background: var(--raisin-black-2);
    padding: 20px;
    border-radius: 8px;
    border-left: 4px solid var(--orange);
}

.tools-grid-modal {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
}

.tool-item-modal {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 12px 16px;
    background: var(--raisin-black-2);
    border-radius: 8px;
    border: 1px solid var(--raisin-black-3);
    transition: all 0.3s ease;
}

.tool-item-modal:hover {
    border-color: var(--orange);
    transform: translateY(-2px);
}

.tool-item-modal ion-icon {
    color: var(--orange);
    font-size: 1.2rem;
}

.tool-item-modal span {
    color: var(--white);
    font-weight: 500;
}

/* Tool tags in challenge cards */
.tool-tag,
.tool-tag-small {
    background: var(--raisin-black-3);
    color: var(--white) !important;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.8rem;
    font-weight: 500;
    border: 1px solid var(--orange);
}

.tool-tag:hover,
.tool-tag-small:hover {
    background: var(--orange);
    color: var(--raisin-black-1) !important;
}

.steps-container {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.step-item {
    display: flex;
    gap: 15px;
    align-items: flex-start;
}

.step-number {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 30px;
    height: 30px;
    background: var(--orange);
    color: var(--raisin-black-1);
    border-radius: 50%;
    font-weight: bold;
    font-size: 0.9rem;
    flex-shrink: 0;
}

.step-content {
    flex: 1;
    padding: 5px 0;
    line-height: 1.5;
}

/* Code Block Styles */
.code-block {
    background: var(--raisin-black-3);
    border: 1px solid var(--raisin-black-2);
    border-radius: 8px;
    margin: 20px 0;
    position: relative;
}

.code-header {
    background: var(--raisin-black-2);
    padding: 10px 15px;
    border-bottom: 1px solid var(--raisin-black-1);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.code-language {
    color: var(--orange);
    font-size: 0.9rem;
    font-weight: 600;
    text-transform: uppercase;
}

.copy-btn {
    background: var(--orange);
    color: var(--white);
    border: none;
    padding: 5px 12px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.8rem;
    transition: all 0.3s ease;
}

.copy-btn:hover {
    background: #e67e22;
    transform: scale(1.05);
}

.code-content {
    padding: 20px;
    font-family: 'Courier New', monospace;
    font-size: 0.9rem;
    line-height: 1.6;
    color: #f8f8f2;
    background: #1a1a1a;
    overflow-x: auto;
}

/* Enhanced Code Block Styles */
.code-block.enhanced {
    margin: 15px 0;
    border-radius: 12px;
    overflow: hidden;
    border: 1px solid var(--raisin-black-3);
    background: var(--raisin-black-2);
}

.code-block.enhanced .code-header {
    background: var(--raisin-black-3);
    padding: 12px 20px;
    border-bottom: 1px solid var(--raisin-black-2);
}

.code-info {
    display: flex;
    align-items: center;
    gap: 8px;
    color: var(--light-gray);
    font-size: 0.9rem;
}

.code-block.enhanced .code-content {
    padding: 20px;
    background: #1a1a1a;
    color: #f8f8f2 !important;
    font-size: 0.9rem;
    line-height: 1.5;
}

.code-block.enhanced .code-content pre,
.code-block.enhanced .code-content code {
    color: #f8f8f2 !important;
    background: transparent;
}

.code-content pre {
    margin: 0;
    white-space: pre-wrap;
    word-wrap: break-word;
}

.flag-container {
    text-align: center;
}

.flag-block {
    display: inline-block;
    min-width: 300px;
}

.flag-content {
    background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
    border: 2px solid var(--orange);
    font-weight: bold;
    font-size: 1.1rem;
    text-align: center;
}

/* Loading and Error States */
.loading-state,
.error-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 60px 20px;
    text-align: center;
    grid-column: 1 / -1;
    background: var(--raisin-black-2);
    border-radius: 12px;
    border: 2px solid var(--raisin-black-3);
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid var(--raisin-black-3);
    border-top: 4px solid var(--orange);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 20px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading-state p,
.error-state p {
    color: var(--light-gray);
    margin: 10px 0;
    font-size: 1.1rem;
}

.error-state ion-icon {
    font-size: 3rem;
    color: #e74c3c;
    margin-bottom: 15px;
}

.error-state h3 {
    color: var(--white);
    margin: 10px 0;
    font-family: var(--ff-oswald);
}

.no-challenges {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 60px 20px;
    text-align: center;
    grid-column: 1 / -1;
    background: var(--raisin-black-2);
    border-radius: 12px;
    border: 2px dashed var(--raisin-black-3);
}

.no-challenges ion-icon {
    font-size: 4rem;
    color: var(--orange);
    margin-bottom: 20px;
    opacity: 0.7;
}

.no-challenges h3 {
    color: var(--white);
    margin: 10px 0;
    font-family: var(--ff-oswald);
    font-size: 1.5rem;
}

.no-challenges p {
    color: var(--light-gray);
    margin: 10px 0 20px;
    font-size: 1rem;
}

/* Enhanced Footer Styles */
.footer {
    background: linear-gradient(135deg, var(--raisin-black-1) 0%, #0a0a0a 100%);
    color: var(--light-gray);
    position: relative;
    overflow: hidden;
}

.footer::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, transparent, var(--orange), transparent);
}

/* Footer Top Section */
.footer-top {
    padding: 60px 0;
    text-align: center;
    border-bottom: 1px solid var(--raisin-black-3);
}

.footer-cta h2 {
    font-family: var(--ff-oswald);
    font-size: 2.5rem;
    color: var(--white);
    margin-bottom: 15px;
    text-transform: uppercase;
}

.footer-cta p {
    font-size: 1.1rem;
    margin-bottom: 30px;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.footer-cta-buttons {
    display: flex;
    gap: 20px;
    justify-content: center;
    flex-wrap: wrap;
}

/* Footer Main Content */
.footer-content {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr 1fr 1.5fr;
    gap: 40px;
    padding: 80px 0 60px;
    border-bottom: 1px solid var(--raisin-black-3);
}

/* Footer Brand */
.footer-brand {
    max-width: 400px;
}

.footer-description {
    line-height: 1.7;
    margin: 20px 0 30px;
    color: var(--light-gray);
}

.footer-stats {
    display: flex;
    gap: 30px;
    margin: 30px 0;
    padding: 20px 0;
    border-top: 1px solid var(--raisin-black-3);
    border-bottom: 1px solid var(--raisin-black-3);
}

.footer-stat {
    text-align: center;
}

.footer-stat .stat-number {
    display: block;
    font-family: var(--ff-oswald);
    font-size: 1.8rem;
    font-weight: 700;
    color: var(--orange);
    margin-bottom: 5px;
}

.footer-stat .stat-label {
    font-size: 0.9rem;
    color: var(--light-gray);
    text-transform: uppercase;
    letter-spacing: 1px;
}

/* Social Links */
.social-links h5 {
    color: var(--white);
    font-family: var(--ff-oswald);
    margin-bottom: 15px;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.social-icons {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.social-link {
    display: flex;
    align-items: center;
    gap: 12px;
    color: var(--light-gray);
    text-decoration: none;
    padding: 8px 12px;
    border-radius: 8px;
    transition: all 0.3s ease;
    border: 1px solid transparent;
}

.social-link:hover {
    color: var(--orange);
    background: rgba(255, 165, 0, 0.1);
    border-color: var(--orange);
    transform: translateX(5px);
}

.social-link ion-icon {
    font-size: 1.2rem;
}

/* Footer Links */
.footer-links h4,
.footer-categories h4,
.footer-resources h4,
.footer-contact h4 {
    color: var(--white);
    font-family: var(--ff-oswald);
    font-size: 1.2rem;
    margin-bottom: 20px;
    text-transform: uppercase;
    letter-spacing: 1px;
    position: relative;
    padding-bottom: 10px;
}

.footer-links h4::after,
.footer-categories h4::after,
.footer-resources h4::after,
.footer-contact h4::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 30px;
    height: 2px;
    background: var(--orange);
}

.footer-links ul,
.footer-categories ul,
.footer-resources ul {
    list-style: none;
    padding: 0;
}

.footer-links li,
.footer-categories li,
.footer-resources li {
    margin-bottom: 12px;
}

.footer-links a,
.footer-categories a,
.footer-resources a {
    color: var(--light-gray);
    text-decoration: none;
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 5px 0;
    transition: all 0.3s ease;
    border-radius: 4px;
}

.footer-links a:hover,
.footer-categories a:hover,
.footer-resources a:hover {
    color: var(--orange);
    transform: translateX(5px);
}

.footer-links a ion-icon,
.footer-categories a ion-icon,
.footer-resources a ion-icon {
    font-size: 1rem;
    opacity: 0.7;
}

/* Contact Info */
.contact-info {
    margin-bottom: 30px;
}

.contact-item {
    display: flex;
    align-items: flex-start;
    gap: 15px;
    margin-bottom: 20px;
    padding: 15px;
    background: var(--raisin-black-2);
    border-radius: 8px;
    border-left: 3px solid var(--orange);
}

.contact-item ion-icon {
    font-size: 1.3rem;
    color: var(--orange);
    margin-top: 2px;
}

.contact-details {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.contact-label {
    font-size: 0.8rem;
    color: var(--orange);
    text-transform: uppercase;
    letter-spacing: 1px;
    font-weight: 600;
}

.contact-details a {
    color: var(--light-gray);
    text-decoration: none;
    transition: color 0.3s ease;
}

.contact-details a:hover {
    color: var(--orange);
}

/* Footer Newsletter */
.footer-newsletter {
    margin-top: 30px;
    padding: 20px;
    background: var(--raisin-black-2);
    border-radius: 8px;
    border: 1px solid var(--raisin-black-3);
}

.footer-newsletter h5 {
    color: var(--white);
    font-family: var(--ff-oswald);
    margin-bottom: 10px;
    text-transform: uppercase;
}

.footer-newsletter p {
    font-size: 0.9rem;
    margin-bottom: 15px;
}

.newsletter-form-footer {
    display: flex;
    gap: 10px;
}

.newsletter-form-footer input {
    flex: 1;
    padding: 10px 15px;
    border: 1px solid var(--raisin-black-3);
    border-radius: 6px;
    background: var(--raisin-black-1);
    color: var(--white);
    font-size: 0.9rem;
}

.newsletter-form-footer input:focus {
    outline: none;
    border-color: var(--orange);
}

.newsletter-form-footer button {
    padding: 10px 15px;
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Footer Middle Section */
.footer-middle {
    padding: 40px 0;
    border-bottom: 1px solid var(--raisin-black-3);
}

.footer-certifications h4 {
    color: var(--white);
    font-family: var(--ff-oswald);
    text-align: center;
    margin-bottom: 25px;
    text-transform: uppercase;
}

.cert-logos {
    display: flex;
    justify-content: center;
    gap: 40px;
    flex-wrap: wrap;
}

.cert-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
    padding: 15px;
    background: var(--raisin-black-2);
    border-radius: 8px;
    border: 1px solid var(--raisin-black-3);
    transition: all 0.3s ease;
}

.cert-item:hover {
    border-color: var(--orange);
    transform: translateY(-3px);
}

.cert-item ion-icon {
    font-size: 2rem;
    color: var(--orange);
}

.cert-item span {
    font-size: 0.8rem;
    color: var(--light-gray);
    text-align: center;
    font-weight: 600;
}

/* Footer Bottom */
.footer-bottom {
    padding: 30px 0;
}

.footer-bottom-content {
    display: grid;
    grid-template-columns: 1fr auto 1fr;
    gap: 30px;
    align-items: center;
}

.footer-bottom-left p {
    margin: 0;
    font-size: 0.9rem;
}

.footer-version {
    font-size: 0.8rem;
    opacity: 0.7;
    margin-top: 5px !important;
}

.footer-badges {
    display: flex;
    gap: 20px;
    flex-wrap: wrap;
    justify-content: center;
}

.badge-item {
    display: flex;
    align-items: center;
    gap: 6px;
    padding: 6px 12px;
    background: var(--raisin-black-2);
    border-radius: 15px;
    font-size: 0.8rem;
    color: var(--light-gray);
    border: 1px solid var(--raisin-black-3);
}

.badge-item ion-icon {
    color: var(--orange);
    font-size: 0.9rem;
}

.footer-bottom-links {
    display: flex;
    gap: 20px;
    flex-wrap: wrap;
    justify-content: flex-end;
}

.footer-bottom-links a {
    color: var(--light-gray);
    text-decoration: none;
    font-size: 0.9rem;
    transition: color 0.3s ease;
}

.footer-bottom-links a:hover {
    color: var(--orange);
}

/* Responsive Design */
@media (max-width: 768px) {
    .blog-hero {
        padding: 120px 0 80px;
    }
    
    .blog-hero-stats {
        gap: 20px;
    }
    
    .stat-number {
        font-size: 2rem;
    }
    
    .categories-grid,
    .methodology-steps,
    .tools-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }
    
    .challenge-filters {
        gap: 10px;
    }
    
    .filter-btn {
        padding: 10px 20px;
        font-size: 0.9rem;
    }
    
    .challenges-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }
    
    .modal-content {
        width: 95%;
        margin: 10% auto;
    }
    
    .modal-body {
        padding: 20px;
    }
}

@media (max-width: 480px) {
    .blog-hero-title {
        font-size: 2rem;
    }

    .blog-hero-subtitle {
        font-size: 0.9rem;
    }

    .category-card,
    .step-card,
    .tool-category {
        padding: 25px 20px;
    }

    .challenge-header,
    .challenge-footer {
        padding: 20px;
    }

    /* Footer Responsive */
    .footer-content {
        grid-template-columns: 1fr;
        gap: 40px;
        text-align: center;
    }

    .footer-cta h2 {
        font-size: 2rem;
    }

    .footer-cta-buttons {
        flex-direction: column;
        align-items: center;
    }

    .footer-stats {
        justify-content: center;
        gap: 20px;
    }

    .social-icons {
        flex-direction: row;
        justify-content: center;
        flex-wrap: wrap;
    }

    .cert-logos {
        gap: 20px;
    }

    .footer-bottom-content {
        grid-template-columns: 1fr;
        text-align: center;
        gap: 20px;
    }

    .footer-badges {
        justify-content: center;
    }

    .footer-bottom-links {
        justify-content: center;
        gap: 15px;
    }
}

@media (max-width: 1024px) {
    .footer-content {
        grid-template-columns: 1fr 1fr;
        gap: 30px;
    }

    .footer-brand {
        grid-column: 1 / -1;
        text-align: center;
        max-width: none;
    }
}

/* Toast Notifications */
.toast {
    position: fixed;
    top: 20px;
    right: 20px;
    background: var(--raisin-black-1);
    color: var(--white);
    padding: 15px 20px;
    border-radius: 8px;
    border-left: 4px solid var(--orange);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
    transform: translateX(400px);
    transition: transform 0.3s ease;
    z-index: 10000;
    max-width: 350px;
    display: flex;
    align-items: center;
    gap: 15px;
}

.toast.show {
    transform: translateX(0);
}

.toast-success {
    border-left-color: #4CAF50;
}

.toast-error {
    border-left-color: #F44336;
}

.toast-warning {
    border-left-color: #FF9800;
}

.toast-info {
    border-left-color: var(--orange);
}

.toast-content {
    display: flex;
    align-items: center;
    gap: 10px;
    flex: 1;
}

.toast-content ion-icon {
    font-size: 1.2rem;
    color: var(--orange);
}

.toast-success .toast-content ion-icon {
    color: #4CAF50;
}

.toast-error .toast-content ion-icon {
    color: #F44336;
}

.toast-warning .toast-content ion-icon {
    color: #FF9800;
}

.toast-message {
    font-size: 0.9rem;
    line-height: 1.4;
}

.toast-close {
    background: none;
    border: none;
    color: var(--light-gray);
    cursor: pointer;
    padding: 5px;
    border-radius: 4px;
    transition: all 0.3s ease;
}

.toast-close:hover {
    background: rgba(255, 255, 255, 0.1);
    color: var(--white);
}

/* Simple Loading States */
.loading-state {
    opacity: 0.7;
    pointer-events: none;
    transition: opacity 0.3s ease;
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none !important;
}

.btn.loading {
    position: relative;
}

.btn.loading::after {
    content: '';
    position: absolute;
    width: 16px;
    height: 16px;
    margin: auto;
    border: 2px solid transparent;
    border-top-color: currentColor;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Floating Action Buttons */
.floating-actions {
    position: fixed;
    bottom: 30px;
    right: 30px;
    z-index: 1000;
}

.fab {
    width: 56px;
    height: 56px;
    border-radius: 50%;
    background: var(--orange);
    color: var(--white);
    border: none;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    box-shadow: 0 4px 20px rgba(255, 165, 0, 0.3);
    transition: all 0.3s ease;
    margin-bottom: 15px;
}

.fab:hover {
    transform: scale(1.1);
    box-shadow: 0 6px 25px rgba(255, 165, 0, 0.4);
}

.main-fab {
    background: var(--orange);
    transform: rotate(0deg);
}

.main-fab.active {
    transform: rotate(45deg);
}

.fab-menu {
    display: flex;
    flex-direction: column-reverse;
    opacity: 0;
    visibility: hidden;
    transform: translateY(20px);
    transition: all 0.3s ease;
}

.fab-menu.active {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.fab-menu .fab {
    width: 48px;
    height: 48px;
    font-size: 1.2rem;
    background: var(--raisin-black-2);
    border: 2px solid var(--orange);
}

/* Back to Top Button */
.back-to-top {
    position: fixed;
    bottom: 30px;
    left: 30px;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: var(--orange);
    color: var(--white);
    border: none;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    box-shadow: 0 4px 15px rgba(255, 165, 0, 0.3);
    transition: all 0.3s ease;
    opacity: 0;
    visibility: hidden;
    transform: translateY(20px);
    z-index: 999;
}

.back-to-top.visible {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.back-to-top:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 20px rgba(255, 165, 0, 0.4);
}

/* Progress Modal Styles */
.progress-overview {
    margin-bottom: 30px;
}

.progress-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.progress-stat {
    text-align: center;
    padding: 20px;
    background: var(--raisin-black-2);
    border-radius: 8px;
    border: 2px solid transparent;
    transition: border-color 0.3s ease;
}

.progress-stat:hover {
    border-color: var(--orange);
}

.progress-number {
    display: block;
    font-family: var(--ff-oswald);
    font-size: 2rem;
    font-weight: 700;
    color: var(--orange);
    margin-bottom: 5px;
}

.progress-label {
    color: var(--light-gray);
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.category-progress-item {
    background: var(--raisin-black-2);
    padding: 20px;
    border-radius: 8px;
    margin-bottom: 15px;
    border-left: 4px solid var(--orange);
}

.category-progress-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
}

.category-name {
    font-weight: 600;
    color: var(--white);
    text-transform: capitalize;
}

.category-percentage {
    color: var(--orange);
    font-weight: 600;
}

.category-progress-bar {
    width: 100%;
    height: 8px;
    background: var(--raisin-black-3);
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 8px;
}

.category-progress-fill {
    height: 100%;
    background: linear-gradient(90deg, var(--orange), #e67e22);
    border-radius: 4px;
    transition: width 0.5s ease;
}

.category-progress-stats {
    font-size: 0.8rem;
    color: var(--light-gray);
}

/* Settings Modal Styles */
.settings-section {
    margin-bottom: 30px;
    padding-bottom: 20px;
    border-bottom: 1px solid var(--raisin-black-3);
}

.settings-section:last-child {
    border-bottom: none;
}

.settings-section h3 {
    color: var(--white);
    font-family: var(--ff-oswald);
    margin-bottom: 20px;
    text-transform: uppercase;
}

.setting-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 15px;
    padding: 10px 0;
}

.setting-item label {
    color: var(--light-gray);
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 10px;
}

.setting-item select,
.setting-item input[type="range"] {
    background: var(--raisin-black-2);
    border: 1px solid var(--raisin-black-3);
    color: var(--white);
    padding: 8px 12px;
    border-radius: 4px;
}

.setting-item input[type="checkbox"] {
    width: 18px;
    height: 18px;
    accent-color: var(--orange);
}

/* Animation Classes */
.fadeInUp {
    animation: fadeInUp 0.6s ease forwards;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.animated {
    animation-fill-mode: both;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
    .toast {
        right: 10px;
        left: 10px;
        max-width: none;
    }

    .floating-actions {
        bottom: 20px;
        right: 20px;
    }

    .back-to-top {
        bottom: 20px;
        left: 20px;
    }

    .progress-stats {
        grid-template-columns: 1fr;
    }
}

/* Advanced Features */
.challenge-info {
    display: flex;
    gap: 15px;
    margin: 20px 0;
    flex-wrap: wrap;
}

.challenge-info .challenge-category,
.challenge-info .challenge-difficulty,
.challenge-info .challenge-points {
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 600;
}

.challenge-info .challenge-points {
    background: var(--orange);
    color: var(--white);
}

.tools-list {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin: 15px 0;
}

.modal-body h2 {
    color: var(--orange);
    font-family: var(--ff-oswald);
    font-size: 2rem;
    margin-bottom: 20px;
    text-transform: uppercase;
}

.modal-body h3 {
    color: var(--white);
    font-family: var(--ff-oswald);
    font-size: 1.4rem;
    margin: 25px 0 15px 0;
    text-transform: uppercase;
    border-bottom: 2px solid var(--orange);
    padding-bottom: 5px;
}

.modal-body h4 {
    color: var(--light-gray);
    font-family: var(--ff-oswald);
    font-size: 1.1rem;
    margin: 20px 0 10px 0;
    text-transform: uppercase;
}

.modal-body ol {
    color: var(--light-gray);
    line-height: 1.8;
    padding-left: 20px;
}

.modal-body ol li {
    margin-bottom: 8px;
}

.modal-body p {
    color: var(--light-gray);
    line-height: 1.6;
    margin-bottom: 15px;
}

/* Syntax Highlighting for Code Blocks */
.code-content {
    background: #1e1e1e;
    color: #d4d4d4;
}

.code-content .comment {
    color: #6a9955;
    font-style: italic;
}

.code-content .string {
    color: #ce9178;
}

.code-content .keyword {
    color: #569cd6;
}

.code-content .function {
    color: #dcdcaa;
}

.code-content .number {
    color: #b5cea8;
}

/* Loading Animation */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid var(--raisin-black-3);
    border-radius: 50%;
    border-top-color: var(--orange);
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Success/Error Messages */
.message {
    padding: 15px 20px;
    border-radius: 8px;
    margin: 15px 0;
    font-weight: 500;
}

.message.success {
    background: rgba(76, 175, 80, 0.1);
    border: 1px solid #4CAF50;
    color: #4CAF50;
}

.message.error {
    background: rgba(244, 67, 54, 0.1);
    border: 1px solid #F44336;
    color: #F44336;
}

.message.info {
    background: rgba(255, 165, 0, 0.1);
    border: 1px solid var(--orange);
    color: var(--orange);
}

/* Tooltip */
.tooltip {
    position: relative;
    display: inline-block;
}

.tooltip .tooltiptext {
    visibility: hidden;
    width: 200px;
    background-color: var(--raisin-black-1);
    color: var(--white);
    text-align: center;
    border-radius: 6px;
    padding: 8px;
    position: absolute;
    z-index: 1;
    bottom: 125%;
    left: 50%;
    margin-left: -100px;
    opacity: 0;
    transition: opacity 0.3s;
    font-size: 0.9rem;
    border: 1px solid var(--orange);
}

.tooltip:hover .tooltiptext {
    visibility: visible;
    opacity: 1;
}

/* Progress Bar */
.progress-bar {
    width: 100%;
    height: 8px;
    background: var(--raisin-black-3);
    border-radius: 4px;
    overflow: hidden;
    margin: 10px 0;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, var(--orange), #e67e22);
    border-radius: 4px;
    transition: width 0.3s ease;
}

/* Badge System */
.badge {
    display: inline-block;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.7rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.badge.new {
    background: #4CAF50;
    color: white;
}

.badge.popular {
    background: var(--orange);
    color: white;
}

.badge.hard {
    background: #F44336;
    color: white;
}

/* Animated Background */
.animated-bg {
    position: relative;
    overflow: hidden;
}

.animated-bg::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 165, 0, 0.1), transparent);
    animation: shimmer 2s infinite;
}

@keyframes shimmer {
    0% { left: -100%; }
    100% { left: 100%; }
}

/* Dark Mode Toggle */
.theme-toggle {
    position: fixed;
    top: 100px;
    right: 20px;
    background: var(--orange);
    color: var(--white);
    border: none;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    cursor: pointer;
    font-size: 1.2rem;
    transition: all 0.3s ease;
    z-index: 100;
    box-shadow: 0 4px 15px rgba(255, 165, 0, 0.3);
}

.theme-toggle:hover {
    transform: scale(1.1);
    box-shadow: 0 6px 20px rgba(255, 165, 0, 0.4);
}

/* Scroll to Top Button */
.scroll-top {
    position: fixed;
    bottom: 30px;
    right: 30px;
    background: var(--orange);
    color: var(--white);
    border: none;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    cursor: pointer;
    font-size: 1.2rem;
    transition: all 0.3s ease;
    opacity: 0;
    visibility: hidden;
    z-index: 100;
}

.scroll-top.visible {
    opacity: 1;
    visibility: visible;
}

.scroll-top:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 20px rgba(255, 165, 0, 0.4);
}

/* No Challenges Message */
.no-challenges {
    grid-column: 1 / -1;
    text-align: center;
    padding: 60px 20px;
    color: var(--light-gray);
}

.no-challenges ion-icon {
    font-size: 4rem;
    color: var(--orange);
    margin-bottom: 20px;
}

.no-challenges h3 {
    color: var(--white);
    margin-bottom: 10px;
    font-family: var(--ff-oswald);
}

/* Load More Button */
.load-more-container {
    text-align: center;
    margin-top: 40px;
}

/* Learning Resources Section */
.learning-resources {
    padding: 100px 0;
    background: var(--raisin-black-1);
}

.resources-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
    margin-top: 50px;
}

.resource-card {
    background: var(--raisin-black-2);
    padding: 40px 30px;
    border-radius: 12px;
    text-align: center;
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.resource-card:hover {
    border-color: var(--orange);
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(255, 165, 0, 0.2);
}

.resource-icon {
    width: 80px;
    height: 80px;
    background: var(--orange);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 20px;
}

.resource-icon ion-icon {
    font-size: 2.5rem;
    color: var(--white);
}

.resource-card h3 {
    font-family: var(--ff-oswald);
    font-size: 1.4rem;
    color: var(--white);
    margin-bottom: 15px;
    text-transform: uppercase;
}

.resource-card p {
    color: var(--light-gray);
    line-height: 1.6;
    margin-bottom: 20px;
}

.resource-features {
    list-style: none;
    padding: 0;
    margin: 20px 0;
    text-align: left;
}

.resource-features li {
    color: var(--light-gray);
    padding: 5px 0;
    position: relative;
    padding-left: 20px;
}

.resource-features li::before {
    content: '✓';
    color: var(--orange);
    font-weight: bold;
    position: absolute;
    left: 0;
}

.resource-link {
    display: inline-block;
    background: var(--orange);
    color: var(--white);
    padding: 12px 25px;
    border-radius: 25px;
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.resource-link:hover {
    background: #e67e22;
    transform: translateY(-2px);
}

/* Advanced Techniques Section */
.advanced-techniques {
    padding: 100px 0;
    background: linear-gradient(135deg, var(--raisin-black-2) 0%, var(--raisin-black-1) 100%);
    position: relative;
    scroll-margin-top: 80px;
}

.advanced-techniques::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, transparent, var(--orange), transparent);
}

.techniques-accordion {
    margin-top: 50px;
}

.technique-item {
    background: var(--raisin-black-1);
    border-radius: 12px;
    margin-bottom: 20px;
    overflow: hidden;
    border: 2px solid transparent;
    transition: border-color 0.3s ease;
}

.technique-item:hover {
    border-color: var(--orange);
}

.technique-header {
    padding: 25px 30px;
    cursor: pointer;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: var(--raisin-black-2);
    transition: all 0.3s ease;
    border: 2px solid transparent;
    border-radius: 8px 8px 0 0;
    user-select: none;
    -webkit-tap-highlight-color: transparent;
    touch-action: manipulation;
}

.technique-header:hover,
.technique-header:focus {
    background: var(--raisin-black-3);
    border-color: var(--orange);
    outline: none;
}

.technique-header.active {
    background: var(--raisin-black-3);
    border-color: var(--orange);
}

.technique-header.touching {
    background: var(--orange);
    transform: scale(0.98);
}

.technique-header.touching h3 {
    color: var(--raisin-black-1);
}

.technique-header.touching ion-icon {
    color: var(--raisin-black-1);
}

.technique-header h3 {
    font-family: var(--ff-oswald);
    font-size: 1.4rem;
    color: var(--white);
    margin: 0;
    text-transform: uppercase;
    transition: color 0.3s ease;
}

.technique-header ion-icon {
    font-size: 1.5rem;
    color: var(--orange);
    transition: all 0.3s ease;
}

.technique-header.active ion-icon {
    transform: rotate(180deg);
}

.technique-content {
    max-height: 0;
    overflow: hidden;
    padding: 0 30px;
    background: var(--raisin-black-1);
    transition: all 0.3s ease-out;
    border: 2px solid transparent;
    border-top: none;
    border-radius: 0 0 8px 8px;
}

.technique-content.active {
    padding: 30px;
    border-color: var(--orange);
}

.technique-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 25px;
}

.technique-card {
    background: var(--raisin-black-2);
    padding: 25px;
    border-radius: 8px;
    border-left: 4px solid var(--orange);
}

.technique-card h4 {
    color: var(--white);
    font-family: var(--ff-oswald);
    margin-bottom: 10px;
    font-size: 1.1rem;
}

.technique-card p {
    color: var(--light-gray);
    line-height: 1.5;
    margin-bottom: 15px;
    font-size: 0.9rem;
}

.technique-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
}

.tag {
    background: var(--raisin-black-3);
    color: var(--orange);
    padding: 4px 10px;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 500;
    border: 1px solid rgba(255, 165, 0, 0.3);
}

/* Newsletter Section */
.newsletter-section {
    padding: 80px 0;
    background: linear-gradient(135deg, var(--raisin-black-1) 0%, var(--raisin-black-2) 100%);
    text-align: center;
}

.newsletter-content h2 {
    font-family: var(--ff-oswald);
    font-size: 2.5rem;
    color: var(--white);
    margin-bottom: 15px;
    text-transform: uppercase;
}

.newsletter-content p {
    color: var(--light-gray);
    font-size: 1.1rem;
    margin-bottom: 30px;
}

.newsletter-form {
    display: flex;
    max-width: 500px;
    margin: 0 auto 40px;
    gap: 15px;
}

.newsletter-form input {
    flex: 1;
    padding: 15px 20px;
    border: 2px solid var(--raisin-black-3);
    border-radius: 25px;
    background: var(--raisin-black-2);
    color: var(--white);
    font-size: 1rem;
}

.newsletter-form input:focus {
    outline: none;
    border-color: var(--orange);
}

.newsletter-features {
    display: flex;
    justify-content: center;
    gap: 40px;
    flex-wrap: wrap;
}

.newsletter-features .feature {
    display: flex;
    align-items: center;
    gap: 10px;
    color: var(--light-gray);
}

/* ========================================
   ENHANCED RESPONSIVE NEWSLETTER SECTION
   ======================================== */

.newsletter-section {
    padding: 100px 0;
    background: linear-gradient(135deg, #1a1a1a 0%, #2a2a2a 50%, #1a1a1a 100%);
    position: relative;
    overflow: hidden;
}

.newsletter-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, transparent, #ff6b35, transparent);
}

.newsletter-section::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, transparent, #ff6b35, transparent);
}

.newsletter-content {
    max-width: 1000px;
    margin: 0 auto;
    text-align: center;
}

.newsletter-header {
    margin-bottom: 50px;
}

.newsletter-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, #ff6b35, #ff8c42);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 30px;
    box-shadow: 0 10px 30px rgba(255, 107, 53, 0.3);
}

.newsletter-icon ion-icon {
    font-size: 2.5rem;
    color: #ffffff;
}

.newsletter-header h2 {
    font-family: 'Oswald', sans-serif;
    font-size: 2.8rem;
    color: #ffffff;
    margin-bottom: 15px;
    line-height: 1.2;
}

.newsletter-subtitle {
    font-size: 1.2rem;
    color: #b0b0b0;
    max-width: 600px;
    margin: 0 auto;
    line-height: 1.6;
}

.newsletter-form-container {
    margin-bottom: 60px;
}

.newsletter-form {
    max-width: 500px;
    margin: 0 auto;
}

.form-group {
    margin-bottom: 20px;
}

.input-wrapper {
    position: relative;
    margin-bottom: 20px;
}

.input-icon {
    position: absolute;
    left: 20px;
    top: 50%;
    transform: translateY(-50%);
    color: #ff6b35;
    font-size: 1.2rem;
    z-index: 2;
}

.newsletter-input {
    width: 100%;
    padding: 18px 20px 18px 55px;
    background: #2a2a2a;
    border: 2px solid #3a3a3a;
    border-radius: 50px;
    color: #ffffff;
    font-size: 1rem;
    transition: all 0.3s ease;
    box-sizing: border-box;
}

.newsletter-input:focus {
    outline: none;
    border-color: #ff6b35;
    background: #333333;
    box-shadow: 0 0 20px rgba(255, 107, 53, 0.2);
}

.newsletter-input::placeholder {
    color: #888888;
}

.newsletter-btn {
    width: 100%;
    padding: 18px 30px;
    background: linear-gradient(135deg, #ff6b35, #ff8c42);
    border: none;
    border-radius: 50px;
    color: #ffffff;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.newsletter-btn:hover {
    background: linear-gradient(135deg, #ff8c42, #ff6b35);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(255, 107, 53, 0.4);
}

.btn-icon {
    font-size: 1.2rem;
    transition: transform 0.3s ease;
}

.newsletter-btn:hover .btn-icon {
    transform: translateX(5px);
}

.form-privacy {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    color: #888888;
    font-size: 0.9rem;
    margin-top: 15px;
}

.form-privacy ion-icon {
    color: #4CAF50;
    font-size: 1rem;
}

.newsletter-features h3 {
    font-family: 'Oswald', sans-serif;
    font-size: 2rem;
    color: #ffffff;
    margin-bottom: 40px;
    text-transform: uppercase;
    letter-spacing: 2px;
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
    margin-bottom: 60px;
}

.feature-card {
    background: #2a2a2a;
    padding: 30px;
    border-radius: 15px;
    border: 2px solid #3a3a3a;
    transition: all 0.3s ease;
    text-align: left;
    position: relative;
    overflow: hidden;
}

.feature-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background: linear-gradient(90deg, #ff6b35, #ff8c42);
    transform: scaleX(0);
    transition: transform 0.3s ease;
}

.feature-card:hover::before {
    transform: scaleX(1);
}

.feature-card:hover {
    border-color: #ff6b35;
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(255, 107, 53, 0.2);
}

.feature-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, #ff6b35, #ff8c42);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 20px;
}

.feature-icon ion-icon {
    font-size: 1.8rem;
    color: #ffffff;
}

.feature-content h4 {
    font-family: 'Oswald', sans-serif;
    font-size: 1.3rem;
    color: #ffffff;
    margin-bottom: 10px;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.feature-content p {
    color: #b0b0b0;
    line-height: 1.6;
    font-size: 0.95rem;
}

.newsletter-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 30px;
    padding: 40px 0;
    border-top: 1px solid #3a3a3a;
}

.newsletter-stats .stat-item {
    text-align: center;
}

.newsletter-stats .stat-number {
    display: block;
    font-family: 'Oswald', sans-serif;
    font-size: 2.5rem;
    font-weight: 700;
    color: #ff6b35;
    margin-bottom: 8px;
    line-height: 1;
}

.newsletter-stats .stat-label {
    color: #b0b0b0;
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 1px;
    font-weight: 500;
}

/* Newsletter Validation & Feedback */
.input-validation {
    margin-top: 10px;
    min-height: 25px;
}

.validation-error,
.validation-success {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 0.9rem;
    padding: 8px 12px;
    border-radius: 8px;
    animation: slideIn 0.3s ease;
}

.validation-error {
    color: #f44336;
    background: rgba(244, 67, 54, 0.1);
    border: 1px solid rgba(244, 67, 54, 0.3);
}

.validation-success {
    color: #4CAF50;
    background: rgba(76, 175, 80, 0.1);
    border: 1px solid rgba(76, 175, 80, 0.3);
}

.validation-error ion-icon,
.validation-success ion-icon {
    font-size: 1rem;
}

/* Loading Spinner for Newsletter */
.loading-spinner-small {
    width: 20px;
    height: 20px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-top: 2px solid #ffffff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

/* Enhanced Toast Notifications */
.toast {
    position: fixed;
    top: 20px;
    right: 20px;
    background: #2a2a2a;
    border: 2px solid #3a3a3a;
    border-radius: 12px;
    padding: 0;
    min-width: 300px;
    max-width: 400px;
    z-index: 10000;
    transform: translateX(100%);
    transition: all 0.3s ease;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.toast.show {
    transform: translateX(0);
}

.toast-success {
    border-color: #4CAF50;
}

.toast-error {
    border-color: #f44336;
}

.toast-info {
    border-color: #ff6b35;
}

.toast-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 15px 20px;
}

.toast-message {
    color: #ffffff;
    font-size: 0.95rem;
    line-height: 1.4;
    flex: 1;
}

.toast-close {
    background: none;
    border: none;
    color: #888888;
    cursor: pointer;
    padding: 5px;
    border-radius: 50%;
    transition: all 0.3s ease;
    margin-left: 10px;
}

.toast-close:hover {
    color: #ffffff;
    background: rgba(255, 255, 255, 0.1);
}

.toast-close ion-icon {
    font-size: 1.2rem;
}

/* Enhanced Responsive Design for CTF Challenge Browser */

/* Large Desktop (1200px and up) */
@media (min-width: 1200px) {
    .challenges-grid {
        grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
        gap: 35px;
    }

    .modal-content {
        max-width: 1000px;
    }

    .tools-grid-modal {
        grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
    }
}

/* Tablet (768px to 991px) */
@media (max-width: 991px) and (min-width: 768px) {
    .challenges-grid {
        grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
        gap: 25px;
    }

    .challenge-filters {
        gap: 12px;
    }

    .filter-btn {
        padding: 10px 20px;
        font-size: 0.9rem;
    }

    .modal-content {
        width: 90%;
        margin: 3% auto;
    }

    .modal-challenge-header {
        padding: 25px;
    }

    .modal-content-section {
        padding: 20px 25px;
    }

    .tools-grid-modal {
        grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
        gap: 12px;
    }
}

/* Mobile Large (576px to 767px) */
@media (max-width: 767px) and (min-width: 576px) {
    .container {
        padding: 0 25px;
    }

    .hero-content h1 {
        font-size: 2.5rem;
        line-height: 1.2;
    }

    .hero-content p {
        font-size: 1rem;
    }

    .challenge-filters {
        flex-direction: column;
        align-items: center;
        gap: 10px;
    }

    .filter-btn {
        width: 100%;
        max-width: 250px;
        padding: 12px 20px;
    }

    .difficulty-filters {
        flex-direction: column;
        align-items: center;
        gap: 8px;
    }

    .diff-filter-btn {
        width: 100%;
        max-width: 200px;
    }

    .search-container {
        margin: 25px 0;
    }

    .search-input {
        font-size: 1rem;
        padding: 15px 50px 15px 20px;
    }

    .challenges-grid {
        grid-template-columns: 1fr !important;
        gap: 20px;
    }

    .challenge-stats {
        grid-template-columns: repeat(2, 1fr);
        gap: 15px;
    }

    .modal-content {
        width: 95%;
        margin: 5% auto;
        max-height: 90vh;
    }

    .modal-challenge-header {
        padding: 20px;
    }

    .challenge-title-section {
        flex-direction: column;
        align-items: flex-start;
        gap: 15px;
    }

    .challenge-actions-modal {
        width: 100%;
        justify-content: space-between;
    }

    .bookmark-btn-modal,
    .complete-btn-modal {
        flex: 1;
        justify-content: center;
    }

    .challenge-meta-modal {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }

    .challenge-progress-bar {
        width: 100%;
        min-width: auto;
    }

    .modal-content-section {
        padding: 20px;
    }

    .tools-grid-modal {
        grid-template-columns: 1fr;
        gap: 10px;
    }

    .steps-container {
        gap: 12px;
    }

    .step-item {
        flex-direction: column;
        gap: 8px;
    }

    .step-number {
        align-self: flex-start;
    }

    .code-content {
        font-size: 0.85rem;
        padding: 15px;
    }

    .flag-block {
        min-width: auto;
        width: 100%;
    }

    /* Newsletter Section Tablet */
    .newsletter-section {
        padding: 80px 0;
    }

    .newsletter-header h2 {
        font-size: 2.4rem;
    }

    .newsletter-subtitle {
        font-size: 1.1rem;
    }

    .features-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 25px;
    }

    .feature-card {
        padding: 28px;
    }

    .newsletter-stats {
        grid-template-columns: repeat(4, 1fr);
        gap: 25px;
    }
}

/* Mobile Small (up to 575px) */
@media (max-width: 575px) {
    .challenge-filters {
        gap: 8px;
    }

    .filter-btn {
        padding: 10px 15px;
        font-size: 0.8rem;
        max-width: 200px;
    }

    .difficulty-filters {
        gap: 6px;
    }

    .diff-filter-btn {
        padding: 8px 12px;
        font-size: 0.8rem;
        max-width: 150px;
    }

    .search-input {
        padding: 12px 45px 12px 15px;
        font-size: 0.9rem;
    }

    .search-icon {
        right: 12px;
        font-size: 1.2rem;
    }

    .challenge-stats {
        grid-template-columns: 1fr;
        gap: 12px;
    }

    .stat-card {
        padding: 20px;
    }

    .stat-value {
        font-size: 2rem;
    }

    .challenges-grid {
        gap: 15px;
    }

    .challenge-card {
        padding: 15px;
    }

    .challenge-title {
        font-size: 1.1rem;
    }

    .challenge-description {
        font-size: 0.85rem;
    }

    .modal-content {
        width: 98%;
        margin: 2% auto;
        border-radius: 8px;
    }

    .modal-header {
        padding: 15px 20px;
    }

    .modal-header h2 {
        font-size: 1.3rem;
    }

    .close-modal {
        font-size: 24px;
    }

    .modal-challenge-header {
        padding: 15px;
    }

    .challenge-title-section h2 {
        font-size: 1.4rem;
    }

    .challenge-actions-modal {
        flex-direction: column;
        width: 100%;
    }

    .bookmark-btn-modal,
    .complete-btn-modal {
        width: 100%;
        justify-content: center;
        padding: 12px;
    }

    .modal-content-section {
        padding: 15px;
    }

    .section-header h3 {
        font-size: 1.1rem;
    }

    .section-header ion-icon {
        font-size: 1.3rem;
    }

    .description-content {
        padding: 15px;
    }

    .tool-item-modal {
        padding: 10px 12px;
    }

    .step-number {
        width: 25px;
        height: 25px;
        font-size: 0.8rem;
    }

    .code-header {
        padding: 10px 15px;
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }

    .code-info {
        font-size: 0.8rem;
    }

    .copy-btn {
        padding: 8px 12px;
        font-size: 0.8rem;
        align-self: flex-end;
    }

    .code-content {
        font-size: 0.8rem;
        padding: 12px;
        line-height: 1.4;
    }

    .flag-content {
        font-size: 1rem;
    }

    /* Advanced Techniques Responsive */
    .technique-header {
        padding: 15px 20px;
    }

    .technique-header h3 {
        font-size: 1.1rem;
    }

    .technique-header ion-icon {
        font-size: 1.3rem;
    }

    .technique-content {
        padding: 0 20px;
    }

    .technique-content.active {
        padding: 20px;
    }

    .technique-grid {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    .technique-card {
        padding: 20px;
    }

    .technique-card h4 {
        font-size: 1rem;
    }

    .technique-card p {
        font-size: 0.85rem;
    }

    /* Newsletter Section Responsive */
    .newsletter-section {
        padding: 60px 0;
    }

    .newsletter-icon {
        width: 60px;
        height: 60px;
        margin-bottom: 20px;
    }

    .newsletter-icon ion-icon {
        font-size: 2rem;
    }

    .newsletter-header h2 {
        font-size: 2rem;
        margin-bottom: 12px;
    }

    .newsletter-subtitle {
        font-size: 1rem;
        padding: 0 15px;
    }

    .newsletter-form-container {
        margin-bottom: 40px;
        padding: 0 15px;
    }

    .newsletter-input {
        padding: 15px 18px 15px 50px;
        font-size: 0.9rem;
    }

    .newsletter-btn {
        padding: 15px 25px;
        font-size: 1rem;
    }

    .newsletter-features h3 {
        font-size: 1.5rem;
        margin-bottom: 30px;
        padding: 0 15px;
    }

    .features-grid {
        grid-template-columns: 1fr;
        gap: 20px;
        padding: 0 15px;
        margin-bottom: 40px;
    }

    .feature-card {
        padding: 25px 20px;
        text-align: center;
    }

    .feature-icon {
        width: 50px;
        height: 50px;
        margin: 0 auto 15px;
    }

    .feature-icon ion-icon {
        font-size: 1.5rem;
    }

    .feature-content h4 {
        font-size: 1.1rem;
        margin-bottom: 8px;
    }

    .feature-content p {
        font-size: 0.9rem;
    }

    .newsletter-stats {
        grid-template-columns: repeat(2, 1fr);
        gap: 20px;
        padding: 30px 15px;
    }

    .newsletter-stats .stat-number {
        font-size: 2rem;
    }

    .newsletter-stats .stat-label {
        font-size: 0.8rem;
    }
}

/* Extra responsive styles for Advanced Techniques */
@media (max-width: 768px) {
    .advanced-techniques {
        padding: 60px 0;
    }

    .techniques-accordion {
        margin-top: 30px;
    }

    .technique-item {
        margin-bottom: 15px;
    }

    .technique-header {
        padding: 20px;
        border-radius: 8px;
    }

    .technique-header h3 {
        font-size: 1.2rem;
    }

    .technique-content {
        padding: 0 20px;
        border-radius: 0 0 8px 8px;
    }

    .technique-content.active {
        padding: 25px 20px;
    }

    .technique-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }
}

@media (max-width: 480px) {
    .technique-header {
        padding: 15px;
    }

    .technique-header h3 {
        font-size: 1rem;
        line-height: 1.2;
    }

    .technique-content.active {
        padding: 20px 15px;
    }

    .technique-card {
        padding: 15px;
    }

    .technique-tags {
        gap: 6px;
    }

    .tag {
        padding: 4px 8px;
        font-size: 0.75rem;
    }
}

/* ========================================
   UNIFIED RESPONSIVE DESIGN SYSTEM
   ======================================== */

/* Mobile Small (320px to 575px) */
@media (max-width: 575px) {
    .container {
        padding: 0 15px;
    }

    /* Typography */
    .blog-hero-title {
        font-size: 2rem;
        line-height: 1.1;
    }

    .blog-hero-subtitle {
        font-size: 0.9rem;
    }

    .section-title {
        font-size: 1.8rem;
    }

    .section-subtitle {
        font-size: 0.9rem;
    }

    /* Hero Section */
    .blog-hero {
        padding: 60px 0 40px;
    }

    .blog-hero-stats {
        gap: 15px;
        flex-direction: column;
        align-items: center;
    }

    .stat-item {
        width: 100%;
        max-width: 200px;
    }

    .stat-number {
        font-size: 2rem;
    }

    /* Sections */
    .ctf-categories,
    .methodology-section,
    .tools-section,
    .challenges-section,
    .advanced-techniques,
    .learning-resources {
        padding: 50px 0;
    }

    /* Grids */
    .categories-grid,
    .methodology-steps,
    .tools-grid,
    .resources-grid {
        grid-template-columns: 1fr;
        gap: 20px;
        margin-top: 30px;
    }

    /* Cards */
    .category-card,
    .step-card,
    .tool-category,
    .resource-card {
        padding: 25px 20px;
    }

    .category-icon {
        width: 60px;
        height: 60px;
    }

    .category-icon ion-icon {
        font-size: 2rem;
    }

    /* Challenge Browser */
    .challenge-filters {
        gap: 8px;
        margin: 30px 0;
    }

    .filter-btn {
        padding: 10px 15px;
        font-size: 0.8rem;
        max-width: 180px;
        width: 100%;
    }

    .difficulty-filters {
        gap: 6px;
        margin: 15px 0;
    }

    .diff-filter-btn {
        padding: 8px 12px;
        font-size: 0.8rem;
        max-width: 140px;
        width: 100%;
    }

    .search-input {
        padding: 12px 45px 12px 15px;
        font-size: 0.9rem;
    }

    .search-icon {
        right: 12px;
        font-size: 1.2rem;
    }

    .challenge-stats {
        grid-template-columns: 1fr;
        gap: 12px;
    }

    .stat-card {
        padding: 20px 15px;
    }

    .stat-value {
        font-size: 2rem;
    }

    .challenges-grid {
        gap: 15px;
        margin-top: 30px;
    }

    .challenge-card {
        padding: 15px;
    }

    .challenge-header {
        padding: 20px 15px;
    }

    .challenge-footer {
        padding: 15px;
    }

    .challenge-title {
        font-size: 1.1rem;
    }

    .challenge-description {
        font-size: 0.85rem;
    }

    /* Modal */
    .modal-content {
        width: 100%;
        height: 100vh;
        margin: 0;
        border-radius: 0;
        max-height: 100vh;
    }

    .modal-header {
        padding: 15px 20px;
    }

    .modal-header h2 {
        font-size: 1.3rem;
    }

    .close-modal {
        font-size: 24px;
        top: 12px;
        right: 20px;
    }

    .modal-challenge-header {
        padding: 15px;
    }

    .challenge-title-section h2 {
        font-size: 1.4rem;
    }

    .challenge-actions-modal {
        flex-direction: column;
        width: 100%;
        gap: 8px;
    }

    .bookmark-btn-modal,
    .complete-btn-modal {
        width: 100%;
        justify-content: center;
        padding: 12px;
    }

    .modal-content-section {
        padding: 15px;
    }

    .section-header h3 {
        font-size: 1.1rem;
    }

    .section-header ion-icon {
        font-size: 1.3rem;
    }

    .description-content {
        padding: 15px;
    }

    .tool-item-modal {
        padding: 10px 12px;
    }

    .step-number {
        width: 25px;
        height: 25px;
        font-size: 0.8rem;
    }

    .code-header {
        padding: 10px 15px;
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }

    .code-info {
        font-size: 0.8rem;
    }

    .copy-btn {
        padding: 8px 12px;
        font-size: 0.8rem;
        align-self: flex-end;
    }

    .code-content {
        font-size: 0.8rem;
        padding: 12px;
        line-height: 1.4;
    }

    .flag-content {
        font-size: 1rem;
    }

    /* Advanced Techniques */
    .technique-header {
        padding: 15px;
    }

    .technique-header h3 {
        font-size: 1rem;
        line-height: 1.2;
    }

    .technique-content.active {
        padding: 20px 15px;
    }

    .technique-card {
        padding: 15px;
    }

    .technique-tags {
        gap: 6px;
    }

    .tag {
        padding: 4px 8px;
        font-size: 0.75rem;
    }

    /* Footer */
    .footer-content {
        grid-template-columns: 1fr;
        gap: 30px;
        text-align: center;
        padding: 60px 0 40px;
    }

    .footer-cta h2 {
        font-size: 1.8rem;
    }

    .footer-cta-buttons {
        flex-direction: column;
        align-items: center;
        gap: 15px;
    }

    .footer-stats {
        justify-content: center;
        gap: 15px;
        flex-wrap: wrap;
    }

    .social-icons {
        justify-content: center;
        flex-wrap: wrap;
        gap: 15px;
    }

    .cert-logos {
        gap: 15px;
        justify-content: center;
    }

    .footer-bottom-content {
        grid-template-columns: 1fr;
        text-align: center;
        gap: 15px;
    }

    .footer-badges {
        justify-content: center;
        gap: 10px;
    }

    .footer-bottom-links {
        justify-content: center;
        gap: 15px;
        flex-wrap: wrap;
    }

    /* Floating Elements */
    .floating-actions {
        bottom: 15px;
        right: 15px;
    }

    .fab {
        width: 50px;
        height: 50px;
        font-size: 1.3rem;
    }

    .back-to-top {
        bottom: 15px;
        left: 15px;
        width: 45px;
        height: 45px;
        font-size: 1.1rem;
    }

    .toast {
        right: 10px;
        left: 10px;
        max-width: none;
        margin: 0 10px;
    }

    /* Progress Modal */
    .progress-stats {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    .progress-stat {
        padding: 15px;
    }

    .progress-number {
        font-size: 1.8rem;
    }
}

/* Touch Improvements for All Devices */
@media (hover: none) and (pointer: coarse) {
    /* Touch-specific styles */
    .filter-btn,
    .diff-filter-btn,
    .bookmark-btn,
    .complete-btn-modal,
    .bookmark-btn-modal,
    .technique-header {
        min-height: 48px;
        min-width: 48px;
    }

    .challenge-card {
        padding: 20px;
    }

    .tool-item-modal {
        padding: 15px;
    }

    .step-item {
        padding: 10px 0;
    }
}

/* High DPI Displays */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    .code-content {
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
    }

    .challenge-card,
    .modal-content,
    .technique-item {
        border-width: 1px;
    }
}

/* Landscape Mobile */
@media (max-height: 500px) and (orientation: landscape) {
    .blog-hero {
        padding: 40px 0 30px;
    }

    .modal-content {
        margin: 2% auto;
        max-height: 95vh;
    }

    .modal-challenge-header {
        padding: 15px 20px;
    }

    .modal-content-section {
        padding: 15px 20px;
    }
}

/* Print Styles */
@media print {
    .floating-actions,
    .back-to-top,
    .toast,
    .fab,
    .theme-toggle {
        display: none !important;
    }

    .modal {
        position: static !important;
        background: none !important;
    }

    .modal-content {
        box-shadow: none !important;
        border: 1px solid #000 !important;
    }

    .code-content {
        background: #f5f5f5 !important;
        color: #000 !important;
    }
}
