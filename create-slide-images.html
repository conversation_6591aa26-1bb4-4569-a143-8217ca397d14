<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Generate Slide Images</title>
    <style>
        body {
            font-family: 'Courier New', monospace;
            background: #0a0e17;
            color: #00ff8c;
            margin: 0;
            padding: 20px;
        }
        
        .terminal {
            background: #1a1a2e;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            border: 1px solid #333;
            width: 600px;
            height: 400px;
            position: relative;
        }
        
        .terminal-header {
            background: #2d2d3a;
            margin: -20px -20px 15px -20px;
            padding: 10px 20px;
            border-radius: 8px 8px 0 0;
            display: flex;
            align-items: center;
        }
        
        .terminal-buttons {
            display: flex;
            gap: 8px;
            margin-right: 15px;
        }
        
        .btn {
            width: 12px;
            height: 12px;
            border-radius: 50%;
        }
        
        .btn.red { background: #ff5f56; }
        .btn.yellow { background: #ffbd2e; }
        .btn.green { background: #27c93f; }
        
        .terminal-title {
            color: #a0a0a0;
            font-size: 14px;
        }
        
        .command-line {
            margin: 10px 0;
            line-height: 1.4;
        }
        
        .prompt {
            color: #00ff8c;
        }
        
        .command {
            color: #ffffff;
        }
        
        .output {
            color: #a0a0a0;
            margin-left: 20px;
        }
        
        .highlight {
            color: #0084ff;
        }
        
        .error {
            color: #ff3860;
        }
        
        .success {
            color: #00ff8c;
        }
    </style>
</head>
<body>
    <h1>Pentesting Slide Images Generator</h1>
    <p>This page generates terminal-style images for the pentesting slides.</p>
    
    <!-- Slide 1: Introduction -->
    <div class="terminal" id="slide1">
        <div class="terminal-header">
            <div class="terminal-buttons">
                <span class="btn red"></span>
                <span class="btn yellow"></span>
                <span class="btn green"></span>
            </div>
            <span class="terminal-title">Introduction to Penetration Testing</span>
        </div>
        <div class="command-line">
            <span class="prompt">root@kali:~#</span>
            <span class="command">whoami</span>
        </div>
        <div class="output">ethical_hacker</div>
        <div class="command-line">
            <span class="prompt">root@kali:~#</span>
            <span class="command">cat /etc/motd</span>
        </div>
        <div class="output">
            Welcome to Kali Linux<br>
            <span class="highlight">Penetration Testing Distribution</span><br>
            <span class="success">Legal authorization required</span><br>
            <span class="success">Ethical hacking only</span>
        </div>
    </div>
    
    <!-- Slide 2: Methodology -->
    <div class="terminal" id="slide2">
        <div class="terminal-header">
            <div class="terminal-buttons">
                <span class="btn red"></span>
                <span class="btn yellow"></span>
                <span class="btn green"></span>
            </div>
            <span class="terminal-title">Penetration Testing Methodology</span>
        </div>
        <div class="command-line">
            <span class="prompt">root@kali:~#</span>
            <span class="command">cat ptes_methodology.txt</span>
        </div>
        <div class="output">
            <span class="highlight">PTES Methodology:</span><br>
            1. Pre-engagement<br>
            2. Intelligence Gathering<br>
            3. Threat Modeling<br>
            4. Vulnerability Analysis<br>
            5. Exploitation<br>
            6. Post Exploitation<br>
            7. Reporting
        </div>
    </div>
    
    <!-- Slide 3: Reconnaissance -->
    <div class="terminal" id="slide3">
        <div class="terminal-header">
            <div class="terminal-buttons">
                <span class="btn red"></span>
                <span class="btn yellow"></span>
                <span class="btn green"></span>
            </div>
            <span class="terminal-title">Information Gathering & Reconnaissance</span>
        </div>
        <div class="command-line">
            <span class="prompt">root@kali:~#</span>
            <span class="command">nslookup target.com</span>
        </div>
        <div class="output">
            Server: *******<br>
            Address: *******#53<br>
            <span class="success">Name: target.com</span><br>
            <span class="success">Address: *************</span>
        </div>
        <div class="command-line">
            <span class="prompt">root@kali:~#</span>
            <span class="command">whois target.com</span>
        </div>
        <div class="output">
            Domain Name: TARGET.COM<br>
            <span class="highlight">Registrar: Example Registrar</span><br>
            Creation Date: 2020-01-01
        </div>
    </div>
    
    <script>
        // This script can be used to convert the terminal divs to images
        // For now, these serve as visual templates
        console.log('Slide images templates created');
        
        // Function to capture div as image (would require html2canvas library)
        function captureSlide(slideId) {
            const element = document.getElementById(slideId);
            // html2canvas(element).then(canvas => {
            //     const link = document.createElement('a');
            //     link.download = slideId + '.png';
            //     link.href = canvas.toDataURL();
            //     link.click();
            // });
        }
    </script>
</body>
</html>
