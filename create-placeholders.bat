@echo off
echo Creating placeholder images...

echo Terminal Interface > "assets_I/images/slides/slide1-intro.png"
echo Terminal Interface > "assets_I/images/slides/slide2-methodology.png"
echo Terminal Interface > "assets_I/images/slides/slide3-recon.png"
echo Terminal Interface > "assets_I/images/slides/slide4-scanning.png"
echo Terminal Interface > "assets_I/images/slides/slide5-vuln-assessment.png"
echo Terminal Interface > "assets_I/images/slides/slide6-web-security.png"
echo Terminal Interface > "assets_I/images/slides/slide7-burp-suite.png"
echo Terminal Interface > "assets_I/images/slides/slide8-network-exploit.png"
echo Terminal Interface > "assets_I/images/slides/slide9-metasploit.png"
echo Terminal Interface > "assets_I/images/slides/slide10-post-exploit.png"
echo Terminal Interface > "assets_I/images/slides/slide11-wireless.png"
echo Terminal Interface > "assets_I/images/slides/slide12-social-eng.png"
echo Terminal Interface > "assets_I/images/slides/slide13-mobile-security.png"
echo Terminal Interface > "assets_I/images/slides/slide14-database-security.png"
echo Terminal Interface > "assets_I/images/slides/slide15-cloud-security.png"
echo Terminal Interface > "assets_I/images/slides/slide16-api-security.png"
echo Terminal Interface > "assets_I/images/slides/slide17-crypto-testing.png"
echo Terminal Interface > "assets_I/images/slides/slide18-ad-security.png"
echo Terminal Interface > "assets_I/images/slides/slide19-iot-security.png"
echo Terminal Interface > "assets_I/images/slides/slide20-container-security.png"
echo Terminal Interface > "assets_I/images/slides/slide21-scada-security.png"
echo Terminal Interface > "assets_I/images/slides/slide22-red-team.png"
echo Terminal Interface > "assets_I/images/slides/slide23-malware-analysis.png"
echo Terminal Interface > "assets_I/images/slides/slide24-forensics.png"
echo Terminal Interface > "assets_I/images/slides/slide25-threat-modeling.png"
echo Terminal Interface > "assets_I/images/slides/slide26-compliance.png"
echo Terminal Interface > "assets_I/images/slides/slide27-automation.png"
echo Terminal Interface > "assets_I/images/slides/slide28-physical-security.png"
echo Terminal Interface > "assets_I/images/slides/slide29-reporting.png"
echo Terminal Interface > "assets_I/images/slides/slide30-legal-ethics.png"
echo Terminal Interface > "assets_I/images/slides/slide31-evasion.png"
echo Terminal Interface > "assets_I/images/slides/slide32-bug-bounty.png"
echo Terminal Interface > "assets_I/images/slides/slide33-emerging-threats.png"
echo Terminal Interface > "assets_I/images/slides/slide34-career.png"
echo Terminal Interface > "assets_I/images/slides/slide35-future.png"

echo Placeholder images created!
